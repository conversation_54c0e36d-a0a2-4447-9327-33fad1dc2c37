#!/bin/bash

# ألوان للطباعة
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة للطباعة الملونة
print_color() {
    printf "${!1}%s${NC}\n" "$2"
}

print_color BLUE "========================================"
print_color BLUE "   اختبار شامل لنظام إدارة الأعمال"
print_color BLUE "========================================"
echo

print_color BLUE "🔍 التحقق من متطلبات النظام..."
echo

# التحقق من Node.js
if ! command -v node &> /dev/null; then
    print_color RED "❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً"
    exit 1
fi
print_color GREEN "✅ Node.js مثبت: $(node --version)"

# التحقق من npm
if ! command -v npm &> /dev/null; then
    print_color RED "❌ npm غير متاح"
    exit 1
fi
print_color GREEN "✅ npm مثبت: $(npm --version)"

# التحقق من PostgreSQL
if ! pg_isready -h localhost -p 5432 &> /dev/null; then
    print_color RED "❌ PostgreSQL غير متاح أو لا يعمل على localhost:5432"
    print_color RED "   يرجى التأكد من تشغيل PostgreSQL"
    exit 1
fi
print_color GREEN "✅ PostgreSQL يعمل على localhost:5432"

echo
print_color BLUE "========================================"
print_color BLUE "   تثبيت التبعيات"
print_color BLUE "========================================"
echo

print_color BLUE "📦 تثبيت تبعيات Node.js..."
if ! npm install; then
    print_color RED "❌ فشل في تثبيت التبعيات"
    exit 1
fi
print_color GREEN "✅ تم تثبيت التبعيات بنجاح"

echo
print_color BLUE "========================================"
print_color BLUE "   اختبار قاعدة البيانات"
print_color BLUE "========================================"
echo

print_color BLUE "🗄️ تشغيل اختبار قاعدة البيانات..."
if ! node test-database.js; then
    print_color RED "❌ فشل اختبار قاعدة البيانات"
    print_color RED "   يرجى مراجعة الأخطاء أعلاه"
    exit 1
fi

echo
print_color BLUE "========================================"
print_color BLUE "   فحص الكود"
print_color BLUE "========================================"
echo

print_color BLUE "🔍 فحص الكود بـ TypeScript..."
if ! npm run type-check; then
    print_color YELLOW "⚠️ توجد تحذيرات في الكود"
    print_color YELLOW "   يمكن المتابعة ولكن يُنصح بإصلاح التحذيرات"
else
    print_color GREEN "✅ لا توجد أخطاء في الكود"
fi

echo
print_color BLUE "========================================"
print_color BLUE "   بناء التطبيق"
print_color BLUE "========================================"
echo

print_color BLUE "🔨 بناء التطبيق..."
if ! npm run build; then
    print_color RED "❌ فشل في بناء التطبيق"
    print_color RED "   يرجى مراجعة الأخطاء أعلاه"
    exit 1
fi
print_color GREEN "✅ تم بناء التطبيق بنجاح"

echo
print_color BLUE "========================================"
print_color BLUE "   تشغيل التطبيق"
print_color BLUE "========================================"
echo

print_color BLUE "🚀 تشغيل التطبيق..."
print_color BLUE "   سيتم فتح التطبيق على http://localhost:3000"
print_color BLUE "   اضغط Ctrl+C لإيقاف التطبيق"
echo

# فتح المتصفح (إذا كان متاحاً)
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:3000 &
elif command -v open &> /dev/null; then
    open http://localhost:3000 &
fi

npm start

echo
print_color BLUE "========================================"
print_color BLUE "   انتهى الاختبار"
print_color BLUE "========================================"
