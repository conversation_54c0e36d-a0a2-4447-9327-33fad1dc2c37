"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_lib_database_activity-logs-db_ts"],{

/***/ "./src/lib/database/activity-logs-db.ts":
/*!**********************************************!*\
  !*** ./src/lib/database/activity-logs-db.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activityLogsDB: function() { return /* binding */ activityLogsDB; }\n/* harmony export */ });\n// نظام قاعدة البيانات للسجلات\n// محاكاة قاعدة البيانات باستخدام localStorage مع IndexedDB كبديل\nclass ActivityLogsDatabase {\n    static getInstance() {\n        if (!ActivityLogsDatabase.instance) {\n            ActivityLogsDatabase.instance = new ActivityLogsDatabase();\n        }\n        return ActivityLogsDatabase.instance;\n    }\n    // فحص توفر IndexedDB\n    isIndexedDBAvailable() {\n        return  true && \"indexedDB\" in window && indexedDB !== null;\n    }\n    // تهيئة قاعدة البيانات\n    async initDB() {\n        // التحقق من وجود IndexedDB\n        if (!this.isIndexedDBAvailable()) {\n            console.warn(\"IndexedDB غير متاح في بيئة الخادم\");\n            return Promise.resolve();\n        }\n        return new Promise((resolve, reject)=>{\n            const request = indexedDB.open(this.dbName, this.version);\n            request.onerror = ()=>{\n                console.error(\"خطأ في فتح قاعدة البيانات\");\n                reject(request.error);\n            };\n            request.onsuccess = ()=>{\n                this.db = request.result;\n                resolve();\n            };\n            request.onupgradeneeded = (event)=>{\n                const db = event.target.result;\n                // إنشاء جدول سجلات الأنشطة\n                if (!db.objectStoreNames.contains(\"activity_logs\")) {\n                    const activityStore = db.createObjectStore(\"activity_logs\", {\n                        keyPath: \"id\"\n                    });\n                    activityStore.createIndex(\"userId\", \"userId\", {\n                        unique: false\n                    });\n                    activityStore.createIndex(\"module\", \"module\", {\n                        unique: false\n                    });\n                    activityStore.createIndex(\"action\", \"action\", {\n                        unique: false\n                    });\n                    activityStore.createIndex(\"severity\", \"severity\", {\n                        unique: false\n                    });\n                    activityStore.createIndex(\"timestamp\", \"timestamp\", {\n                        unique: false\n                    });\n                }\n                // إنشاء جدول سجلات تسجيل الدخول\n                if (!db.objectStoreNames.contains(\"login_logs\")) {\n                    const loginStore = db.createObjectStore(\"login_logs\", {\n                        keyPath: \"id\"\n                    });\n                    loginStore.createIndex(\"userId\", \"userId\", {\n                        unique: false\n                    });\n                    loginStore.createIndex(\"success\", \"success\", {\n                        unique: false\n                    });\n                    loginStore.createIndex(\"timestamp\", \"timestamp\", {\n                        unique: false\n                    });\n                }\n                // إنشاء جدول الإحصائيات اليومية\n                if (!db.objectStoreNames.contains(\"daily_stats\")) {\n                    const statsStore = db.createObjectStore(\"daily_stats\", {\n                        keyPath: \"date\"\n                    });\n                    statsStore.createIndex(\"date\", \"date\", {\n                        unique: true\n                    });\n                }\n                // إنشاء جدول التنبيهات الأمنية\n                if (!db.objectStoreNames.contains(\"security_alerts\")) {\n                    const alertsStore = db.createObjectStore(\"security_alerts\", {\n                        keyPath: \"id\"\n                    });\n                    alertsStore.createIndex(\"userId\", \"userId\", {\n                        unique: false\n                    });\n                    alertsStore.createIndex(\"severity\", \"severity\", {\n                        unique: false\n                    });\n                    alertsStore.createIndex(\"timestamp\", \"timestamp\", {\n                        unique: false\n                    });\n                    alertsStore.createIndex(\"resolved\", \"resolved\", {\n                        unique: false\n                    });\n                }\n            };\n        });\n    }\n    // حفظ سجل نشاط\n    async saveActivityLog(log) {\n        // التحقق من وجود IndexedDB\n        if (!this.isIndexedDBAvailable()) {\n            console.warn(\"IndexedDB غير متاح - تم تجاهل حفظ السجل\");\n            return Promise.resolve();\n        }\n        if (!this.db) await this.initDB();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"activity_logs\"\n            ], \"readwrite\");\n            const store = transaction.objectStore(\"activity_logs\");\n            const request = store.add(log);\n            request.onsuccess = ()=>{\n                // تحديث الإحصائيات اليومية\n                this.updateDailyStats(log);\n                // فحص التنبيهات الأمنية\n                this.checkSecurityAlerts(log);\n                resolve();\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // حفظ سجل تسجيل دخول\n    async saveLoginLog(log) {\n        // التحقق من وجود IndexedDB\n        if (!this.isIndexedDBAvailable()) {\n            console.warn(\"IndexedDB غير متاح - تم تجاهل حفظ سجل تسجيل الدخول\");\n            return Promise.resolve();\n        }\n        if (!this.db) await this.initDB();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"login_logs\"\n            ], \"readwrite\");\n            const store = transaction.objectStore(\"login_logs\");\n            const request = store.add(log);\n            request.onsuccess = ()=>{\n                // فحص محاولات تسجيل الدخول المشبوهة\n                this.checkSuspiciousLogins(log);\n                resolve();\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // الحصول على سجلات المستخدم\n    async getUserLogs(userId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        if (!this.db) await this.initDB();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"activity_logs\"\n            ], \"readonly\");\n            const store = transaction.objectStore(\"activity_logs\");\n            const index = store.index(\"userId\");\n            const request = index.getAll(userId);\n            request.onsuccess = ()=>{\n                const logs = request.result.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, limit);\n                resolve(logs);\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // الحصول على سجلات تسجيل الدخول للمستخدم\n    async getUserLoginLogs(userId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        if (!this.db) await this.initDB();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"login_logs\"\n            ], \"readonly\");\n            const store = transaction.objectStore(\"login_logs\");\n            const index = store.index(\"userId\");\n            const request = index.getAll(userId);\n            request.onsuccess = ()=>{\n                const logs = request.result.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, limit);\n                resolve(logs);\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // الحصول على جميع السجلات مع فلترة\n    async getAllLogs(filters) {\n        // التحقق من وجود IndexedDB\n        if (!this.isIndexedDBAvailable()) {\n            console.warn(\"IndexedDB غير متاح - إرجاع قائمة فارغة\");\n            return Promise.resolve([]);\n        }\n        if (!this.db) await this.initDB();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"activity_logs\"\n            ], \"readonly\");\n            const store = transaction.objectStore(\"activity_logs\");\n            const request = store.getAll();\n            request.onsuccess = ()=>{\n                let logs = request.result;\n                // تطبيق الفلاتر\n                if (filters) {\n                    if (filters.userId) {\n                        logs = logs.filter((log)=>log.userId === filters.userId);\n                    }\n                    if (filters.module) {\n                        logs = logs.filter((log)=>log.module === filters.module);\n                    }\n                    if (filters.action) {\n                        logs = logs.filter((log)=>log.action.includes(filters.action));\n                    }\n                    if (filters.severity) {\n                        logs = logs.filter((log)=>log.severity === filters.severity);\n                    }\n                    if (filters.startDate) {\n                        logs = logs.filter((log)=>log.timestamp >= filters.startDate);\n                    }\n                    if (filters.endDate) {\n                        logs = logs.filter((log)=>log.timestamp <= filters.endDate);\n                    }\n                }\n                // ترتيب حسب التاريخ\n                logs.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n                // تحديد العدد\n                const limit = (filters === null || filters === void 0 ? void 0 : filters.limit) || 100;\n                resolve(logs.slice(0, limit));\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // تحديث الإحصائيات اليومية\n    async updateDailyStats(log) {\n        const today = new Date().toISOString().split(\"T\")[0];\n        const transaction = this.db.transaction([\n            \"daily_stats\"\n        ], \"readwrite\");\n        const store = transaction.objectStore(\"daily_stats\");\n        const request = store.get(today);\n        request.onsuccess = ()=>{\n            let stats = request.result || {\n                date: today,\n                totalLogs: 0,\n                criticalLogs: 0,\n                moduleStats: {},\n                userStats: {},\n                actionStats: {}\n            };\n            stats.totalLogs++;\n            if (log.severity === \"critical\") stats.criticalLogs++;\n            // إحصائيات الموديولات\n            stats.moduleStats[log.module] = (stats.moduleStats[log.module] || 0) + 1;\n            // إحصائيات المستخدمين\n            stats.userStats[log.userId] = (stats.userStats[log.userId] || 0) + 1;\n            // إحصائيات الأنشطة\n            stats.actionStats[log.action] = (stats.actionStats[log.action] || 0) + 1;\n            store.put(stats);\n        };\n    }\n    // فحص التنبيهات الأمنية\n    async checkSecurityAlerts(log) {\n        const alerts = [];\n        // تنبيه للأنشطة الحرجة\n        if (log.severity === \"critical\") {\n            alerts.push({\n                id: \"critical_\".concat(Date.now()),\n                type: \"critical_activity\",\n                userId: log.userId,\n                userName: log.userName,\n                severity: \"high\",\n                message: \"نشاط حرج: \".concat(log.action, \" في \").concat(log.module),\n                details: log.details,\n                timestamp: new Date().toISOString(),\n                resolved: false,\n                relatedLogId: log.id\n            });\n        }\n        // تنبيه لحذف المستخدمين\n        if (log.action.includes(\"حذف مستخدم\")) {\n            alerts.push({\n                id: \"user_deletion_\".concat(Date.now()),\n                type: \"user_deletion\",\n                userId: log.userId,\n                userName: log.userName,\n                severity: \"critical\",\n                message: \"تم حذف مستخدم بواسطة \".concat(log.userName),\n                details: log.details,\n                timestamp: new Date().toISOString(),\n                resolved: false,\n                relatedLogId: log.id\n            });\n        }\n        // تنبيه لتعديل الإعدادات\n        if (log.action.includes(\"تعديل الإعدادات\")) {\n            alerts.push({\n                id: \"settings_change_\".concat(Date.now()),\n                type: \"settings_change\",\n                userId: log.userId,\n                userName: log.userName,\n                severity: \"medium\",\n                message: \"تم تعديل إعدادات النظام بواسطة \".concat(log.userName),\n                details: log.details,\n                timestamp: new Date().toISOString(),\n                resolved: false,\n                relatedLogId: log.id\n            });\n        }\n        // حفظ التنبيهات\n        if (alerts.length > 0) {\n            const transaction = this.db.transaction([\n                \"security_alerts\"\n            ], \"readwrite\");\n            const store = transaction.objectStore(\"security_alerts\");\n            alerts.forEach((alert)=>{\n                store.add(alert);\n            });\n        }\n    }\n    // فحص محاولات تسجيل الدخول المشبوهة\n    async checkSuspiciousLogins(log) {\n        if (!log.success) {\n            // فحص محاولات فاشلة متتالية\n            const recentFailures = await this.getRecentFailedLogins(log.userId, 5);\n            if (recentFailures.length >= 3) {\n                const alert = {\n                    id: \"failed_logins_\".concat(Date.now()),\n                    type: \"multiple_failed_logins\",\n                    userId: log.userId,\n                    userName: log.userName,\n                    severity: \"high\",\n                    message: \"محاولات تسجيل دخول فاشلة متعددة للمستخدم \".concat(log.userName),\n                    details: \"\".concat(recentFailures.length, \" محاولات فاشلة في آخر 5 دقائق\"),\n                    timestamp: new Date().toISOString(),\n                    resolved: false,\n                    relatedLogId: log.id\n                };\n                const transaction = this.db.transaction([\n                    \"security_alerts\"\n                ], \"readwrite\");\n                const store = transaction.objectStore(\"security_alerts\");\n                store.add(alert);\n            }\n        }\n    }\n    // الحصول على محاولات تسجيل الدخول الفاشلة الأخيرة\n    async getRecentFailedLogins(userId, minutes) {\n        const cutoffTime = new Date(Date.now() - minutes * 60 * 1000).toISOString();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"login_logs\"\n            ], \"readonly\");\n            const store = transaction.objectStore(\"login_logs\");\n            const index = store.index(\"userId\");\n            const request = index.getAll(userId);\n            request.onsuccess = ()=>{\n                const failedLogins = request.result.filter((log)=>!log.success && log.timestamp >= cutoffTime).sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n                resolve(failedLogins);\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // الحصول على التنبيهات الأمنية\n    async getSecurityAlerts(filters) {\n        if (!this.db) await this.initDB();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"security_alerts\"\n            ], \"readonly\");\n            const store = transaction.objectStore(\"security_alerts\");\n            const request = store.getAll();\n            request.onsuccess = ()=>{\n                let alerts = request.result;\n                if (filters) {\n                    if (filters.resolved !== undefined) {\n                        alerts = alerts.filter((alert)=>alert.resolved === filters.resolved);\n                    }\n                    if (filters.severity) {\n                        alerts = alerts.filter((alert)=>alert.severity === filters.severity);\n                    }\n                }\n                alerts.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n                const limit = (filters === null || filters === void 0 ? void 0 : filters.limit) || 50;\n                resolve(alerts.slice(0, limit));\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // حل تنبيه أمني\n    async resolveSecurityAlert(alertId) {\n        if (!this.db) await this.initDB();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"security_alerts\"\n            ], \"readwrite\");\n            const store = transaction.objectStore(\"security_alerts\");\n            const getRequest = store.get(alertId);\n            getRequest.onsuccess = ()=>{\n                const alert = getRequest.result;\n                if (alert) {\n                    alert.resolved = true;\n                    alert.resolvedAt = new Date().toISOString();\n                    const putRequest = store.put(alert);\n                    putRequest.onsuccess = ()=>resolve();\n                    putRequest.onerror = ()=>reject(putRequest.error);\n                } else {\n                    reject(new Error(\"Alert not found\"));\n                }\n            };\n            getRequest.onerror = ()=>reject(getRequest.error);\n        });\n    }\n    // أرشفة السجلات القديمة\n    async archiveOldLogs() {\n        let daysToKeep = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 90;\n        if (!this.db) await this.initDB();\n        const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000).toISOString();\n        let archivedCount = 0;\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"activity_logs\"\n            ], \"readwrite\");\n            const store = transaction.objectStore(\"activity_logs\");\n            const index = store.index(\"timestamp\");\n            const range = IDBKeyRange.upperBound(cutoffDate);\n            const request = index.openCursor(range);\n            request.onsuccess = (event)=>{\n                const cursor = event.target.result;\n                if (cursor) {\n                    // يمكن هنا نقل السجل إلى أرشيف منفصل قبل الحذف\n                    cursor.delete();\n                    archivedCount++;\n                    cursor.continue();\n                } else {\n                    resolve(archivedCount);\n                }\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // الحصول على إحصائيات متقدمة\n    async getAdvancedStats() {\n        let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;\n        const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString();\n        const logs = await this.getAllLogs({\n            startDate,\n            limit: 10000\n        });\n        const stats = {\n            totalLogs: logs.length,\n            criticalLogs: logs.filter((log)=>log.severity === \"critical\").length,\n            highLogs: logs.filter((log)=>log.severity === \"high\").length,\n            mediumLogs: logs.filter((log)=>log.severity === \"medium\").length,\n            lowLogs: logs.filter((log)=>log.severity === \"low\").length,\n            moduleBreakdown: {},\n            actionBreakdown: {},\n            userBreakdown: {},\n            dailyBreakdown: {},\n            topUsers: [],\n            topActions: [],\n            topModules: []\n        };\n        // تجميع الإحصائيات\n        logs.forEach((log)=>{\n            // إحصائيات الموديولات\n            stats.moduleBreakdown[log.module] = (stats.moduleBreakdown[log.module] || 0) + 1;\n            // إحصائيات الأنشطة\n            stats.actionBreakdown[log.action] = (stats.actionBreakdown[log.action] || 0) + 1;\n            // إحصائيات المستخدمين\n            const userKey = \"\".concat(log.userId, \":\").concat(log.userName);\n            stats.userBreakdown[userKey] = (stats.userBreakdown[userKey] || 0) + 1;\n            // إحصائيات يومية\n            const day = log.timestamp.split(\"T\")[0];\n            stats.dailyBreakdown[day] = (stats.dailyBreakdown[day] || 0) + 1;\n        });\n        // ترتيب القوائم العلوية\n        stats.topUsers = Object.entries(stats.userBreakdown).map((param)=>{\n            let [userKey, count] = param;\n            const [userId, userName] = userKey.split(\":\");\n            return {\n                userId,\n                userName,\n                count\n            };\n        }).sort((a, b)=>b.count - a.count).slice(0, 10);\n        stats.topActions = Object.entries(stats.actionBreakdown).map((param)=>{\n            let [action, count] = param;\n            return {\n                action,\n                count\n            };\n        }).sort((a, b)=>b.count - a.count).slice(0, 10);\n        stats.topModules = Object.entries(stats.moduleBreakdown).map((param)=>{\n            let [module, count] = param;\n            return {\n                module,\n                count\n            };\n        }).sort((a, b)=>b.count - a.count).slice(0, 10);\n        return stats;\n    }\n    constructor(){\n        this.dbName = \"activity_logs_db\";\n        this.version = 1;\n        this.db = null;\n        // تأخير التهيئة حتى يتم استدعاؤها من المتصفح\n        if (true) {\n            this.initDB();\n        }\n    }\n}\n// تصدير المثيل الوحيد\nconst activityLogsDB = ActivityLogsDatabase.getInstance();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/database/activity-logs-db.ts\n"));

/***/ })

}]);