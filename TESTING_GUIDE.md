# دليل اختبار نظام إدارة الأعمال

## نظرة عامة

هذا الدليل يوضح كيفية اختبار نظام إدارة الأعمال للتأكد من أن الكود يعمل بشكل جيد مع قاعدة البيانات PostgreSQL.

## المتطلبات الأساسية

### 1. البرامج المطلوبة
- **Node.js** (الإصدار 18 أو أحدث)
- **npm** (يأتي مع Node.js)
- **PostgreSQL** (الإصدار 12 أو أحدث)
- **pgAdmin** (اختياري - لإدارة قاعدة البيانات بصرياً)

### 2. إعدادات قاعدة البيانات
- **المضيف**: localhost
- **المنفذ**: 5432
- **قاعدة البيانات**: V_Connect
- **المستخدم**: openpg
- **كلمة المرور**: V@admin010

## طرق الاختبار

### الطريقة الأولى: الاختبار التلقائي (مُوصى بها)

#### لنظام Windows:
```bash
# تشغيل سكريبت الاختبار التلقائي
run-tests.bat
```

#### لنظام Linux/Mac:
```bash
# تشغيل سكريبت الاختبار التلقائي
./run-tests.sh
```

### الطريقة الثانية: الاختبار اليدوي

#### 1. تثبيت التبعيات
```bash
npm install
```

#### 2. اختبار قاعدة البيانات
```bash
node test-database.js
```

#### 3. فحص الكود
```bash
npm run type-check
```

#### 4. بناء التطبيق
```bash
npm run build
```

#### 5. تشغيل التطبيق
```bash
npm run dev
```

## اختبارات قاعدة البيانات

### 1. اختبار الاتصال
- يتحقق من إمكانية الاتصال بقاعدة البيانات
- يعرض معلومات الإصدار والوقت الحالي

### 2. إنشاء الجداول
- ينشئ جميع الجداول المطلوبة
- يتحقق من بنية الجداول

### 3. اختبار العمليات الأساسية (CRUD)
- **إنشاء**: إدراج بيانات تجريبية
- **قراءة**: استعلام البيانات
- **تحديث**: تعديل البيانات الموجودة
- **حذف**: حذف البيانات (اختياري)

### 4. اختبار الأداء
- قياس سرعة الاستعلامات
- مراقبة استخدام الذاكرة

### 5. عرض الإحصائيات
- حجم قاعدة البيانات
- عدد الجداول
- عدد الاتصالات النشطة

## اختبار واجهة المستخدم

### 1. صفحة تسجيل الدخول
- اختبار تسجيل الدخول بحساب المدير
- التحقق من رسائل الخطأ

### 2. لوحة التحكم
- عرض الإحصائيات العامة
- التنقل بين الوحدات

### 3. إدارة المنتجات
- إضافة منتج جديد
- تعديل منتج موجود
- البحث عن المنتجات
- حذف منتج

### 4. إدارة العملاء
- إضافة عميل جديد
- تعديل بيانات العميل
- البحث عن العملاء

### 5. المبيعات
- إنشاء عرض أسعار
- تحويل إلى طلب مبيعات
- إنشاء فاتورة

## مراقبة حالة النظام

### صفحة مراقبة النظام
يمكن الوصول إلى صفحة مراقبة حالة النظام عبر:
```
http://localhost:3000/system-status
```

### API فحص الحالة
```
GET http://localhost:3000/api/system/health-check
```

## استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها

#### 1. فشل الاتصال بقاعدة البيانات
**الخطأ**: `ECONNREFUSED` أو `connection refused`

**الحلول**:
- تأكد من تشغيل PostgreSQL
- تحقق من إعدادات الاتصال
- تأكد من صحة اسم المستخدم وكلمة المرور

```bash
# التحقق من حالة PostgreSQL
pg_isready -h localhost -p 5432

# إعادة تشغيل PostgreSQL (Windows)
net stop postgresql-x64-14
net start postgresql-x64-14

# إعادة تشغيل PostgreSQL (Linux)
sudo systemctl restart postgresql
```

#### 2. قاعدة البيانات غير موجودة
**الخطأ**: `database "V_Connect" does not exist`

**الحل**:
```sql
-- الاتصال بـ PostgreSQL كمدير
psql -h localhost -p 5432 -U postgres

-- إنشاء قاعدة البيانات
CREATE DATABASE "V_Connect";

-- إنشاء المستخدم (إذا لم يكن موجوداً)
CREATE USER openpg WITH PASSWORD 'V@admin010';

-- منح الصلاحيات
GRANT ALL PRIVILEGES ON DATABASE "V_Connect" TO openpg;
```

#### 3. مشاكل في الصلاحيات
**الخطأ**: `permission denied` أو `access denied`

**الحل**:
```sql
-- منح جميع الصلاحيات للمستخدم
GRANT ALL PRIVILEGES ON DATABASE "V_Connect" TO openpg;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO openpg;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO openpg;
```

#### 4. أخطاء في بناء التطبيق
**الخطأ**: أخطاء TypeScript أو أخطاء في البناء

**الحلول**:
- تحقق من ملف `tsconfig.json`
- تأكد من تثبيت جميع التبعيات
- امسح مجلد `.next` وأعد البناء

```bash
# مسح الملفات المؤقتة
rm -rf .next
rm -rf node_modules
npm install
npm run build
```

#### 5. مشاكل في المنافذ
**الخطأ**: `Port 3000 is already in use`

**الحل**:
```bash
# العثور على العملية التي تستخدم المنفذ
netstat -ano | findstr :3000

# إنهاء العملية (Windows)
taskkill /PID <process_id> /F

# إنهاء العملية (Linux/Mac)
kill -9 <process_id>

# أو استخدام منفذ مختلف
npm run dev -- -p 3001
```

## نتائج الاختبار المتوقعة

### ✅ نجح الاختبار إذا:
- تم الاتصال بقاعدة البيانات بنجاح
- تم إنشاء جميع الجداول المطلوبة
- تعمل جميع العمليات الأساسية (CRUD)
- لا توجد أخطاء في بناء التطبيق
- يمكن الوصول للتطبيق على http://localhost:3000
- تعمل جميع الصفحات بدون أخطاء
- البيانات محفوظة بشكل صحيح

### ❌ فشل الاختبار إذا:
- فشل الاتصال بقاعدة البيانات
- أخطاء في إنشاء الجداول
- فشل في العمليات الأساسية
- أخطاء في بناء التطبيق
- عدم إمكانية الوصول للتطبيق
- وجود أخطاء في الصفحات
- فقدان أو تلف في البيانات

## التحقق من الأداء

### مؤشرات الأداء المقبولة:
- **وقت الاستجابة لقاعدة البيانات**: أقل من 100ms
- **وقت تحميل الصفحة**: أقل من 2 ثانية
- **استخدام الذاكرة**: أقل من 80%
- **حجم قاعدة البيانات**: يعتمد على كمية البيانات

### أدوات مراقبة الأداء:
- صفحة مراقبة النظام: `/system-status`
- أدوات المطور في المتصفح (F12)
- سجلات الخادم في وحدة التحكم

## الخطوات التالية

بعد نجاح الاختبارات:

1. **إعداد البيانات الحقيقية**: إزالة البيانات التجريبية وإدخال البيانات الفعلية
2. **إعداد النسخ الاحتياطية**: تكوين نظام النسخ الاحتياطي التلقائي
3. **تكوين الأمان**: تغيير كلمات المرور الافتراضية
4. **التدريب**: تدريب المستخدمين على النظام
5. **المراقبة**: إعداد نظام مراقبة مستمر للأداء

## الدعم والمساعدة

في حالة مواجهة مشاكل:

1. راجع سجلات الأخطاء في وحدة التحكم
2. تحقق من صفحة مراقبة النظام
3. راجع هذا الدليل للحلول الشائعة
4. تواصل مع فريق الدعم الفني
