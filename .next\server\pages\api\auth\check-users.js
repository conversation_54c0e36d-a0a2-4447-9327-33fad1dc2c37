"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/check-users";
exports.ids = ["pages/api/auth/check-users"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Fcheck-users&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Ccheck-users.ts&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Fcheck-users&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Ccheck-users.ts&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_check_users_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\check-users.ts */ \"(api)/./src/pages/api/auth/check-users.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_check_users_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_check_users_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/check-users\",\n        pathname: \"/api/auth/check-users\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_check_users_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Fcheck-users&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Ccheck-users.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/check-users.ts":
/*!*******************************************!*\
  !*** ./src/pages/api/auth/check-users.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        // استخدام قاعدة البيانات الافتراضية\n        const config = {\n            host: \"localhost\",\n            port: 5432,\n            database: \"V_Connect\",\n            user: \"openpg\",\n            password: \"V@admin010\"\n        };\n        // إنشاء اتصال بقاعدة البيانات\n        const pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n            host: config.host,\n            port: config.port,\n            database: config.database,\n            user: config.user,\n            password: config.password\n        });\n        try {\n            // اختبار الاتصال\n            const client = await pool.connect();\n            await client.query(\"SELECT NOW()\");\n            client.release();\n            // التحقق من وجود جدول المستخدمين\n            const tableCheck = await pool.query(`\n        SELECT EXISTS (\n          SELECT FROM information_schema.tables\n          WHERE table_schema = 'public'\n          AND table_name = 'users'\n        )\n      `);\n            if (!tableCheck.rows[0].exists) {\n                await pool.end();\n                return res.status(200).json({\n                    success: true,\n                    hasUsers: false,\n                    message: \"جدول المستخدمين غير موجود\"\n                });\n            }\n            // التحقق من وجود مستخدمين\n            const userCount = await pool.query(\"SELECT COUNT(*) as count FROM users\");\n            const usersExist = parseInt(userCount.rows[0].count) > 0;\n            await pool.end();\n            return res.status(200).json({\n                success: true,\n                hasUsers: usersExist,\n                message: usersExist ? \"يوجد مستخدمون في النظام\" : \"لا يوجد مستخدمون في النظام\"\n            });\n        } catch (dbError) {\n            await pool.end();\n            return res.status(500).json({\n                success: false,\n                hasUsers: false,\n                message: \"فشل الاتصال بقاعدة البيانات\"\n            });\n        }\n    } catch (error) {\n        console.error(\"Error checking users:\", error);\n        return res.status(500).json({\n            success: false,\n            hasUsers: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/check-users.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Fcheck-users&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Ccheck-users.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();