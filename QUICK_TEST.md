# اختبار سريع للنظام - Quick System Test

## 🚀 اختبار في 5 دقائق

### الخطوة 1: التحقق من المتطلبات (30 ثانية)

```bash
# تحقق من Node.js
node --version

# تحقق من PostgreSQL
pg_isready -h localhost -p 5432
```

### الخطوة 2: اختبار قاعدة البيانات (1 دقيقة)

```bash
# اختبار سريع للاتصال
npm run test-db-quick

# أو
node quick-db-test.js
```

### الخطوة 3: اختبار شامل (2 دقيقة)

```bash
# اختبار شامل لقاعدة البيانات
npm run test-db

# أو
node test-database.js
```

### الخطوة 4: تشغيل التطبيق (1 دقيقة)

```bash
# تشغيل التطبيق
npm run dev
```

### الخطوة 5: اختبار واجهة المستخدم (30 ثانية)

1. افتح المتصفح على `http://localhost:3000`
2. اذهب إلى `/system-status` للتحقق من حالة النظام
3. اذهب إلى `/setup` لإنشاء حساب مدير

## ✅ النتائج المتوقعة

### اختبار قاعدة البيانات ناجح:
```
🎉 جميع الاختبارات نجحت! قاعدة البيانات تعمل بشكل مثالي.
✅ نجح: 5/5 اختبار
```

### التطبيق يعمل:
- صفحة تسجيل الدخول تظهر
- صفحة مراقبة النظام تعرض حالة "سليم"
- يمكن إنشاء حساب مدير جديد

## ❌ إذا فشل الاختبار

### مشكلة في قاعدة البيانات:
```bash
# إنشاء قاعدة البيانات
psql -U postgres -c "CREATE DATABASE \"V_Connect\""

# إنشاء المستخدم
psql -U postgres -c "CREATE USER openpg WITH PASSWORD 'V@admin010'"

# منح الصلاحيات
psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE \"V_Connect\" TO openpg"
```

### مشكلة في التطبيق:
```bash
# إعادة تثبيت التبعيات
rm -rf node_modules
npm install

# إعادة بناء التطبيق
npm run build
```

## 🔧 اختبار تلقائي كامل

### Windows:
```bash
run-tests.bat
```

### Linux/Mac:
```bash
./run-tests.sh
```

## 📊 مراقبة النظام

- **صفحة المراقبة**: `http://localhost:3000/system-status`
- **API الحالة**: `http://localhost:3000/api/system/health-check`

## 🎯 الخطوات التالية

بعد نجاح الاختبار:
1. إزالة البيانات التجريبية
2. إدخال البيانات الحقيقية
3. تدريب المستخدمين
4. إعداد النسخ الاحتياطية

---

**⚡ نصيحة:** احفظ هذا الملف للاختبار السريع في المستقبل!
