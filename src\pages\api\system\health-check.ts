import { NextApiRequest, NextApiResponse } from 'next'
import { Pool } from 'pg'

interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'degraded'
  timestamp: string
  checks: {
    database: {
      status: 'up' | 'down'
      responseTime?: number
      error?: string
      details?: {
        host: string
        database: string
        tablesCount?: number
        activeConnections?: number
      }
    }
    application: {
      status: 'up' | 'down'
      uptime: number
      memory: {
        used: number
        total: number
        percentage: number
      }
    }
    dependencies: {
      nodejs: string
      nextjs: string
      postgresql?: string
    }
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<HealthCheckResult>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      checks: {
        database: { status: 'down', error: 'Method not allowed' },
        application: { 
          status: 'down', 
          uptime: 0,
          memory: { used: 0, total: 0, percentage: 0 }
        },
        dependencies: { nodejs: '', nextjs: '' }
      }
    })
  }

  const startTime = Date.now()
  const result: HealthCheckResult = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    checks: {
      database: { status: 'down' },
      application: {
        status: 'up',
        uptime: process.uptime(),
        memory: {
          used: 0,
          total: 0,
          percentage: 0
        }
      },
      dependencies: {
        nodejs: process.version,
        nextjs: '14.0.4' // من package.json
      }
    }
  }

  // فحص ذاكرة التطبيق
  const memoryUsage = process.memoryUsage()
  result.checks.application.memory = {
    used: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
    total: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
    percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)
  }

  // فحص قاعدة البيانات
  try {
    const dbConfig = {
      host: 'localhost',
      port: 5432,
      database: 'V_Connect',
      user: 'openpg',
      password: 'V@admin010',
      connectionTimeoutMillis: 5000,
    }

    const pool = new Pool(dbConfig)
    const dbStartTime = Date.now()

    try {
      // اختبار الاتصال
      const client = await pool.connect()
      
      // الحصول على معلومات قاعدة البيانات
      const versionResult = await client.query('SELECT version()')
      const tablesResult = await client.query(`
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `)
      const connectionsResult = await client.query(`
        SELECT COUNT(*) as count 
        FROM pg_stat_activity 
        WHERE datname = 'V_Connect'
      `)

      const dbResponseTime = Date.now() - dbStartTime

      result.checks.database = {
        status: 'up',
        responseTime: dbResponseTime,
        details: {
          host: dbConfig.host,
          database: dbConfig.database,
          tablesCount: parseInt(tablesResult.rows[0].count),
          activeConnections: parseInt(connectionsResult.rows[0].count)
        }
      }

      // استخراج إصدار PostgreSQL
      const versionString = versionResult.rows[0].version
      const versionMatch = versionString.match(/PostgreSQL (\d+\.\d+)/)
      if (versionMatch) {
        result.checks.dependencies.postgresql = versionMatch[1]
      }

      client.release()
      await pool.end()

    } catch (dbError) {
      await pool.end()
      throw dbError
    }

  } catch (error) {
    result.checks.database = {
      status: 'down',
      error: error instanceof Error ? error.message : 'Unknown database error'
    }
    result.status = 'unhealthy'
  }

  // تحديد الحالة العامة
  if (result.checks.database.status === 'down') {
    result.status = 'unhealthy'
  } else if (result.checks.database.responseTime && result.checks.database.responseTime > 1000) {
    result.status = 'degraded'
  } else if (result.checks.application.memory.percentage > 90) {
    result.status = 'degraded'
  }

  // تحديد رمز الاستجابة
  const statusCode = result.status === 'healthy' ? 200 : 
                    result.status === 'degraded' ? 200 : 503

  return res.status(statusCode).json(result)
}
