<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل المستخدم - نظام إدارة الأعمال</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            direction: rtl;
        }
        
        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2563eb;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            color: #666;
            font-size: 1.2em;
        }
        
        .toc {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border: 1px solid #e2e8f0;
        }
        
        .toc h2 {
            color: #2563eb;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        .toc ul {
            list-style: none;
        }
        
        .toc li {
            margin-bottom: 8px;
            padding-right: 20px;
        }
        
        .toc a {
            color: #4f46e5;
            text-decoration: none;
            font-weight: 500;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
        
        .section {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }
        
        .section h2 {
            color: #2563eb;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e2e8f0;
        }
        
        .section h3 {
            color: #1e40af;
            font-size: 1.4em;
            margin: 25px 0 15px 0;
        }
        
        .section h4 {
            color: #3730a3;
            font-size: 1.2em;
            margin: 20px 0 10px 0;
        }
        
        .section p {
            margin-bottom: 15px;
            text-align: justify;
        }
        
        .section ul, .section ol {
            margin: 15px 0;
            padding-right: 30px;
        }
        
        .section li {
            margin-bottom: 8px;
        }
        
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
            direction: ltr;
            text-align: left;
        }
        
        .highlight {
            background: #fef3c7;
            padding: 15px;
            border-radius: 6px;
            border-right: 4px solid #f59e0b;
            margin: 15px 0;
        }
        
        .warning {
            background: #fef2f2;
            padding: 15px;
            border-radius: 6px;
            border-right: 4px solid #ef4444;
            margin: 15px 0;
        }
        
        .success {
            background: #f0fdf4;
            padding: 15px;
            border-radius: 6px;
            border-right: 4px solid #22c55e;
            margin: 15px 0;
        }
        
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e2e8f0;
        }
        
        th {
            background: #2563eb;
            color: white;
            font-weight: 600;
        }
        
        tr:hover {
            background: #f8fafc;
        }
        
        .step {
            background: #f1f5f9;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border-right: 4px solid #2563eb;
        }
        
        .step-number {
            background: #2563eb;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 2px solid #e2e8f0;
            color: #666;
        }
        
        @media print {
            body {
                font-size: 12pt;
            }
            
            .container {
                max-width: none;
                margin: 0;
                padding: 15px;
            }
            
            .section {
                page-break-inside: avoid;
            }
            
            .code-block {
                page-break-inside: avoid;
            }
            
            a {
                color: #000 !important;
                text-decoration: none !important;
            }
        }
        
        .icon {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-left: 8px;
            vertical-align: middle;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .feature-card h4 {
            color: #2563eb;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>دليل المستخدم</h1>
            <p>نظام إدارة الأعمال المتكامل</p>
            <p style="font-size: 0.9em; margin-top: 10px;">الإصدار 1.0 - 2024</p>
        </div>

        <!-- Table of Contents -->
        <div class="toc">
            <h2>📋 فهرس المحتويات</h2>
            <ul>
                <li><a href="#introduction">1. مقدمة عن النظام</a></li>
                <li><a href="#getting-started">2. البدء السريع</a></li>
                <li><a href="#login">3. تسجيل الدخول</a></li>
                <li><a href="#dashboard">4. لوحة التحكم</a></li>
                <li><a href="#products">5. إدارة المنتجات</a></li>
                <li><a href="#customers">6. إدارة العملاء</a></li>
                <li><a href="#sales">7. إدارة المبيعات</a></li>
                <li><a href="#purchases">8. إدارة المشتريات</a></li>
                <li><a href="#inventory">9. إدارة المخزون</a></li>
                <li><a href="#accounting">10. المحاسبة</a></li>
                <li><a href="#maintenance">11. الصيانة الفنية</a></li>
                <li><a href="#reports">12. التقارير</a></li>
                <li><a href="#settings">13. الإعدادات</a></li>
                <li><a href="#troubleshooting">14. حل المشاكل</a></li>
            </ul>
        </div>

        <!-- Introduction -->
        <div class="section" id="introduction">
            <h2>🌟 1. مقدمة عن النظام</h2>
            
            <p>نظام إدارة الأعمال المتكامل هو حل شامل لإدارة جميع جوانب عملك التجاري. يوفر النظام أدوات متقدمة لإدارة المبيعات، المشتريات، المخزون، العملاء، والمحاسبة في واجهة واحدة سهلة الاستخدام.</p>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🛒 إدارة المبيعات</h4>
                    <p>عروض أسعار، طلبات، فواتير، ومرتجعات</p>
                </div>
                <div class="feature-card">
                    <h4>📦 إدارة المخزون</h4>
                    <p>تتبع المنتجات والكميات في الوقت الفعلي</p>
                </div>
                <div class="feature-card">
                    <h4>👥 إدارة العملاء</h4>
                    <p>قاعدة بيانات شاملة للعملاء والموردين</p>
                </div>
                <div class="feature-card">
                    <h4>💰 المحاسبة</h4>
                    <p>تتبع الإيرادات والمصروفات والأرباح</p>
                </div>
            </div>

            <div class="highlight">
                <strong>💡 نصيحة:</strong> يدعم النظام اللغتين العربية والإنجليزية ويمكن التبديل بينهما من أعلى الصفحة.
            </div>
        </div>

        <!-- Getting Started -->
        <div class="section" id="getting-started">
            <h2>🚀 2. البدء السريع</h2>
            
            <h3>متطلبات النظام</h3>
            <ul>
                <li>متصفح ويب حديث (Chrome, Firefox, Safari, Edge)</li>
                <li>اتصال بالإنترنت</li>
                <li>دقة شاشة لا تقل عن 1024x768</li>
            </ul>

            <h3>الوصول للنظام</h3>
            <div class="step">
                <span class="step-number">1</span>
                افتح متصفح الويب واذهب إلى عنوان النظام
            </div>
            <div class="step">
                <span class="step-number">2</span>
                ستظهر لك صفحة تسجيل الدخول
            </div>
            <div class="step">
                <span class="step-number">3</span>
                أدخل اسم المستخدم وكلمة المرور
            </div>
            <div class="step">
                <span class="step-number">4</span>
                اضغط على "تسجيل الدخول"
            </div>

            <div class="success">
                <strong>✅ مبروك!</strong> أنت الآن داخل النظام ويمكنك البدء في استخدام جميع الميزات.
            </div>
        </div>

        <!-- Login -->
        <div class="section" id="login">
            <h2>🔐 3. تسجيل الدخول</h2>
            
            <h3>صفحة تسجيل الدخول</h3>
            <p>صفحة تسجيل الدخول هي نقطة البداية للوصول إلى النظام. تحتوي على:</p>
            
            <ul>
                <li><strong>حقل اسم المستخدم:</strong> أدخل اسم المستخدم الخاص بك</li>
                <li><strong>حقل كلمة المرور:</strong> أدخل كلمة المرور</li>
                <li><strong>زر تسجيل الدخول:</strong> للدخول إلى النظام</li>
                <li><strong>خيار تذكرني:</strong> للبقاء مسجلاً للدخول</li>
            </ul>

            <h3>أنواع المستخدمين</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>نوع المستخدم</th>
                            <th>الصلاحيات</th>
                            <th>الوصول</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>مدير النظام</td>
                            <td>جميع الصلاحيات</td>
                            <td>كامل</td>
                        </tr>
                        <tr>
                            <td>مدير</td>
                            <td>إدارة الفرع والمخزون</td>
                            <td>محدود</td>
                        </tr>
                        <tr>
                            <td>موظف</td>
                            <td>المبيعات والمشتريات</td>
                            <td>أساسي</td>
                        </tr>
                        <tr>
                            <td>كاشير</td>
                            <td>نقطة البيع فقط</td>
                            <td>محدود جداً</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="warning">
                <strong>⚠️ تحذير:</strong> لا تشارك بيانات تسجيل الدخول مع أي شخص آخر. تأكد من تسجيل الخروج عند الانتهاء.
            </div>
        </div>

        <!-- Dashboard -->
        <div class="section" id="dashboard">
            <h2>📊 4. لوحة التحكم</h2>
            
            <p>لوحة التحكم هي الصفحة الرئيسية التي تظهر بعد تسجيل الدخول. تعرض ملخصاً شاملاً لحالة العمل.</p>

            <h3>مكونات لوحة التحكم</h3>
            
            <h4>البطاقات الإحصائية</h4>
            <ul>
                <li><strong>إجمالي المبيعات:</strong> مبيعات اليوم والشهر</li>
                <li><strong>عدد الطلبات:</strong> طلبات جديدة ومعلقة</li>
                <li><strong>المخزون:</strong> المنتجات المتاحة والناقصة</li>
                <li><strong>العملاء:</strong> عدد العملاء الجدد</li>
            </ul>

            <h4>الرسوم البيانية</h4>
            <ul>
                <li><strong>مبيعات الأسبوع:</strong> رسم بياني يوضح المبيعات اليومية</li>
                <li><strong>أفضل المنتجات:</strong> المنتجات الأكثر مبيعاً</li>
                <li><strong>حالة الطلبات:</strong> توزيع الطلبات حسب الحالة</li>
            </ul>

            <h4>الإشعارات والتنبيهات</h4>
            <ul>
                <li>منتجات قاربت على النفاد</li>
                <li>طلبات تحتاج موافقة</li>
                <li>فواتير مستحقة الدفع</li>
                <li>مهام الصيانة المعلقة</li>
            </ul>

            <div class="highlight">
                <strong>💡 نصيحة:</strong> تحديث البيانات في لوحة التحكم يتم تلقائياً كل دقيقة. يمكنك أيضاً الضغط على زر التحديث للحصول على أحدث البيانات.
            </div>
        </div>

        <div class="footer">
            <p>© 2024 نظام إدارة الأعمال المتكامل - جميع الحقوق محفوظة</p>
            <p>للدعم الفني: <EMAIL></p>
        </div>
    </div>
</body>
</html>
