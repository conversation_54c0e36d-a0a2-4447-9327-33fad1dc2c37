import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  RefreshCw, 
  Database, 
  Server, 
  Clock,
  MemoryStick,
  HardDrive,
  Activity
} from 'lucide-react'

interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'degraded'
  timestamp: string
  checks: {
    database: {
      status: 'up' | 'down'
      responseTime?: number
      error?: string
      details?: {
        host: string
        database: string
        tablesCount?: number
        activeConnections?: number
      }
    }
    application: {
      status: 'up' | 'down'
      uptime: number
      memory: {
        used: number
        total: number
        percentage: number
      }
    }
    dependencies: {
      nodejs: string
      nextjs: string
      postgresql?: string
    }
  }
}

export default function SystemStatus() {
  const [healthData, setHealthData] = useState<HealthCheckResult | null>(null)
  const [loading, setLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchHealthData = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/system/health-check')
      const data = await response.json()
      setHealthData(data)
      setLastUpdated(new Date())
    } catch (error) {
      console.error('خطأ في جلب بيانات حالة النظام:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchHealthData()
    
    // تحديث تلقائي كل 30 ثانية
    const interval = setInterval(fetchHealthData, 30000)
    return () => clearInterval(interval)
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'up':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'degraded':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'unhealthy':
      case 'down':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      healthy: 'default',
      up: 'default',
      degraded: 'secondary',
      unhealthy: 'destructive',
      down: 'destructive'
    } as const

    const labels = {
      healthy: 'سليم',
      up: 'يعمل',
      degraded: 'بطيء',
      unhealthy: 'معطل',
      down: 'معطل'
    }

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {labels[status as keyof typeof labels] || status}
      </Badge>
    )
  }

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    
    if (hours > 0) {
      return `${hours}س ${minutes}د ${secs}ث`
    } else if (minutes > 0) {
      return `${minutes}د ${secs}ث`
    } else {
      return `${secs}ث`
    }
  }

  const formatBytes = (bytes: number) => {
    return `${bytes} ميجابايت`
  }

  if (loading && !healthData) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="mr-2">جاري تحميل حالة النظام...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* العنوان والتحديث */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">حالة النظام</h1>
          <p className="text-muted-foreground">
            مراقبة حالة التطبيق وقاعدة البيانات
          </p>
        </div>
        <div className="flex items-center gap-4">
          {lastUpdated && (
            <div className="text-sm text-muted-foreground">
              آخر تحديث: {lastUpdated.toLocaleTimeString('ar-EG')}
            </div>
          )}
          <Button 
            onClick={fetchHealthData} 
            disabled={loading}
            variant="outline"
            size="sm"
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 animate-spin ml-2" />
            ) : (
              <RefreshCw className="h-4 w-4 ml-2" />
            )}
            تحديث
          </Button>
        </div>
      </div>

      {healthData && (
        <>
          {/* الحالة العامة */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {getStatusIcon(healthData.status)}
                الحالة العامة للنظام
              </CardTitle>
              <CardDescription>
                تم الفحص في: {new Date(healthData.timestamp).toLocaleString('ar-EG')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                {getStatusBadge(healthData.status)}
                <span className="text-sm text-muted-foreground">
                  {healthData.status === 'healthy' && 'جميع الخدمات تعمل بشكل طبيعي'}
                  {healthData.status === 'degraded' && 'بعض الخدمات تعمل ببطء'}
                  {healthData.status === 'unhealthy' && 'توجد مشاكل في النظام'}
                </span>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* حالة قاعدة البيانات */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  قاعدة البيانات
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>الحالة</span>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(healthData.checks.database.status)}
                    {getStatusBadge(healthData.checks.database.status)}
                  </div>
                </div>

                {healthData.checks.database.responseTime && (
                  <div className="flex items-center justify-between">
                    <span>وقت الاستجابة</span>
                    <span className="text-sm">
                      {healthData.checks.database.responseTime}ms
                    </span>
                  </div>
                )}

                {healthData.checks.database.details && (
                  <>
                    <Separator />
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>المضيف</span>
                        <span>{healthData.checks.database.details.host}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>قاعدة البيانات</span>
                        <span>{healthData.checks.database.details.database}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>عدد الجداول</span>
                        <span>{healthData.checks.database.details.tablesCount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>الاتصالات النشطة</span>
                        <span>{healthData.checks.database.details.activeConnections}</span>
                      </div>
                    </div>
                  </>
                )}

                {healthData.checks.database.error && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-sm text-red-600">
                      {healthData.checks.database.error}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* حالة التطبيق */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Server className="h-5 w-5" />
                  التطبيق
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>الحالة</span>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(healthData.checks.application.status)}
                    {getStatusBadge(healthData.checks.application.status)}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    وقت التشغيل
                  </span>
                  <span className="text-sm">
                    {formatUptime(healthData.checks.application.uptime)}
                  </span>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <MemoryStick className="h-4 w-4" />
                      استخدام الذاكرة
                    </span>
                    <span className="text-sm">
                      {healthData.checks.application.memory.percentage}%
                    </span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        healthData.checks.application.memory.percentage > 80 
                          ? 'bg-red-500' 
                          : healthData.checks.application.memory.percentage > 60 
                          ? 'bg-yellow-500' 
                          : 'bg-green-500'
                      }`}
                      style={{ 
                        width: `${healthData.checks.application.memory.percentage}%` 
                      }}
                    />
                  </div>
                  
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>
                      مستخدم: {formatBytes(healthData.checks.application.memory.used)}
                    </span>
                    <span>
                      إجمالي: {formatBytes(healthData.checks.application.memory.total)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* معلومات التبعيات */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <HardDrive className="h-5 w-5" />
                إصدارات التبعيات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                <div className="flex justify-between">
                  <span>Node.js</span>
                  <span className="font-mono">
                    {healthData.checks.dependencies.nodejs}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Next.js</span>
                  <span className="font-mono">
                    {healthData.checks.dependencies.nextjs}
                  </span>
                </div>
                {healthData.checks.dependencies.postgresql && (
                  <div className="flex justify-between">
                    <span>PostgreSQL</span>
                    <span className="font-mono">
                      {healthData.checks.dependencies.postgresql}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
