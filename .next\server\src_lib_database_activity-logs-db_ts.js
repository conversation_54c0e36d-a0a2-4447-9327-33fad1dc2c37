"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_lib_database_activity-logs-db_ts";
exports.ids = ["src_lib_database_activity-logs-db_ts"];
exports.modules = {

/***/ "./src/lib/database/activity-logs-db.ts":
/*!**********************************************!*\
  !*** ./src/lib/database/activity-logs-db.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activityLogsDB: () => (/* binding */ activityLogsDB)\n/* harmony export */ });\n// نظام قاعدة البيانات للسجلات\n// محاكاة قاعدة البيانات باستخدام localStorage مع IndexedDB كبديل\nclass ActivityLogsDatabase {\n    constructor(){\n        this.dbName = \"activity_logs_db\";\n        this.version = 1;\n        this.db = null;\n        // تأخير التهيئة حتى يتم استدعاؤها من المتصفح\n        if (false) {}\n    }\n    static getInstance() {\n        if (!ActivityLogsDatabase.instance) {\n            ActivityLogsDatabase.instance = new ActivityLogsDatabase();\n        }\n        return ActivityLogsDatabase.instance;\n    }\n    // فحص توفر IndexedDB\n    isIndexedDBAvailable() {\n        return  false && 0;\n    }\n    // تهيئة قاعدة البيانات\n    async initDB() {\n        // التحقق من وجود IndexedDB\n        if (!this.isIndexedDBAvailable()) {\n            console.warn(\"IndexedDB غير متاح في بيئة الخادم\");\n            return Promise.resolve();\n        }\n        return new Promise((resolve, reject)=>{\n            const request = indexedDB.open(this.dbName, this.version);\n            request.onerror = ()=>{\n                console.error(\"خطأ في فتح قاعدة البيانات\");\n                reject(request.error);\n            };\n            request.onsuccess = ()=>{\n                this.db = request.result;\n                resolve();\n            };\n            request.onupgradeneeded = (event)=>{\n                const db = event.target.result;\n                // إنشاء جدول سجلات الأنشطة\n                if (!db.objectStoreNames.contains(\"activity_logs\")) {\n                    const activityStore = db.createObjectStore(\"activity_logs\", {\n                        keyPath: \"id\"\n                    });\n                    activityStore.createIndex(\"userId\", \"userId\", {\n                        unique: false\n                    });\n                    activityStore.createIndex(\"module\", \"module\", {\n                        unique: false\n                    });\n                    activityStore.createIndex(\"action\", \"action\", {\n                        unique: false\n                    });\n                    activityStore.createIndex(\"severity\", \"severity\", {\n                        unique: false\n                    });\n                    activityStore.createIndex(\"timestamp\", \"timestamp\", {\n                        unique: false\n                    });\n                }\n                // إنشاء جدول سجلات تسجيل الدخول\n                if (!db.objectStoreNames.contains(\"login_logs\")) {\n                    const loginStore = db.createObjectStore(\"login_logs\", {\n                        keyPath: \"id\"\n                    });\n                    loginStore.createIndex(\"userId\", \"userId\", {\n                        unique: false\n                    });\n                    loginStore.createIndex(\"success\", \"success\", {\n                        unique: false\n                    });\n                    loginStore.createIndex(\"timestamp\", \"timestamp\", {\n                        unique: false\n                    });\n                }\n                // إنشاء جدول الإحصائيات اليومية\n                if (!db.objectStoreNames.contains(\"daily_stats\")) {\n                    const statsStore = db.createObjectStore(\"daily_stats\", {\n                        keyPath: \"date\"\n                    });\n                    statsStore.createIndex(\"date\", \"date\", {\n                        unique: true\n                    });\n                }\n                // إنشاء جدول التنبيهات الأمنية\n                if (!db.objectStoreNames.contains(\"security_alerts\")) {\n                    const alertsStore = db.createObjectStore(\"security_alerts\", {\n                        keyPath: \"id\"\n                    });\n                    alertsStore.createIndex(\"userId\", \"userId\", {\n                        unique: false\n                    });\n                    alertsStore.createIndex(\"severity\", \"severity\", {\n                        unique: false\n                    });\n                    alertsStore.createIndex(\"timestamp\", \"timestamp\", {\n                        unique: false\n                    });\n                    alertsStore.createIndex(\"resolved\", \"resolved\", {\n                        unique: false\n                    });\n                }\n            };\n        });\n    }\n    // حفظ سجل نشاط\n    async saveActivityLog(log) {\n        // التحقق من وجود IndexedDB\n        if (!this.isIndexedDBAvailable()) {\n            console.warn(\"IndexedDB غير متاح - تم تجاهل حفظ السجل\");\n            return Promise.resolve();\n        }\n        if (!this.db) await this.initDB();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"activity_logs\"\n            ], \"readwrite\");\n            const store = transaction.objectStore(\"activity_logs\");\n            const request = store.add(log);\n            request.onsuccess = ()=>{\n                // تحديث الإحصائيات اليومية\n                this.updateDailyStats(log);\n                // فحص التنبيهات الأمنية\n                this.checkSecurityAlerts(log);\n                resolve();\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // حفظ سجل تسجيل دخول\n    async saveLoginLog(log) {\n        // التحقق من وجود IndexedDB\n        if (!this.isIndexedDBAvailable()) {\n            console.warn(\"IndexedDB غير متاح - تم تجاهل حفظ سجل تسجيل الدخول\");\n            return Promise.resolve();\n        }\n        if (!this.db) await this.initDB();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"login_logs\"\n            ], \"readwrite\");\n            const store = transaction.objectStore(\"login_logs\");\n            const request = store.add(log);\n            request.onsuccess = ()=>{\n                // فحص محاولات تسجيل الدخول المشبوهة\n                this.checkSuspiciousLogins(log);\n                resolve();\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // الحصول على سجلات المستخدم\n    async getUserLogs(userId, limit = 50) {\n        if (!this.db) await this.initDB();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"activity_logs\"\n            ], \"readonly\");\n            const store = transaction.objectStore(\"activity_logs\");\n            const index = store.index(\"userId\");\n            const request = index.getAll(userId);\n            request.onsuccess = ()=>{\n                const logs = request.result.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, limit);\n                resolve(logs);\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // الحصول على سجلات تسجيل الدخول للمستخدم\n    async getUserLoginLogs(userId, limit = 20) {\n        if (!this.db) await this.initDB();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"login_logs\"\n            ], \"readonly\");\n            const store = transaction.objectStore(\"login_logs\");\n            const index = store.index(\"userId\");\n            const request = index.getAll(userId);\n            request.onsuccess = ()=>{\n                const logs = request.result.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, limit);\n                resolve(logs);\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // الحصول على جميع السجلات مع فلترة\n    async getAllLogs(filters) {\n        // التحقق من وجود IndexedDB\n        if (!this.isIndexedDBAvailable()) {\n            console.warn(\"IndexedDB غير متاح - إرجاع قائمة فارغة\");\n            return Promise.resolve([]);\n        }\n        if (!this.db) await this.initDB();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"activity_logs\"\n            ], \"readonly\");\n            const store = transaction.objectStore(\"activity_logs\");\n            const request = store.getAll();\n            request.onsuccess = ()=>{\n                let logs = request.result;\n                // تطبيق الفلاتر\n                if (filters) {\n                    if (filters.userId) {\n                        logs = logs.filter((log)=>log.userId === filters.userId);\n                    }\n                    if (filters.module) {\n                        logs = logs.filter((log)=>log.module === filters.module);\n                    }\n                    if (filters.action) {\n                        logs = logs.filter((log)=>log.action.includes(filters.action));\n                    }\n                    if (filters.severity) {\n                        logs = logs.filter((log)=>log.severity === filters.severity);\n                    }\n                    if (filters.startDate) {\n                        logs = logs.filter((log)=>log.timestamp >= filters.startDate);\n                    }\n                    if (filters.endDate) {\n                        logs = logs.filter((log)=>log.timestamp <= filters.endDate);\n                    }\n                }\n                // ترتيب حسب التاريخ\n                logs.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n                // تحديد العدد\n                const limit = filters?.limit || 100;\n                resolve(logs.slice(0, limit));\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // تحديث الإحصائيات اليومية\n    async updateDailyStats(log) {\n        const today = new Date().toISOString().split(\"T\")[0];\n        const transaction = this.db.transaction([\n            \"daily_stats\"\n        ], \"readwrite\");\n        const store = transaction.objectStore(\"daily_stats\");\n        const request = store.get(today);\n        request.onsuccess = ()=>{\n            let stats = request.result || {\n                date: today,\n                totalLogs: 0,\n                criticalLogs: 0,\n                moduleStats: {},\n                userStats: {},\n                actionStats: {}\n            };\n            stats.totalLogs++;\n            if (log.severity === \"critical\") stats.criticalLogs++;\n            // إحصائيات الموديولات\n            stats.moduleStats[log.module] = (stats.moduleStats[log.module] || 0) + 1;\n            // إحصائيات المستخدمين\n            stats.userStats[log.userId] = (stats.userStats[log.userId] || 0) + 1;\n            // إحصائيات الأنشطة\n            stats.actionStats[log.action] = (stats.actionStats[log.action] || 0) + 1;\n            store.put(stats);\n        };\n    }\n    // فحص التنبيهات الأمنية\n    async checkSecurityAlerts(log) {\n        const alerts = [];\n        // تنبيه للأنشطة الحرجة\n        if (log.severity === \"critical\") {\n            alerts.push({\n                id: `critical_${Date.now()}`,\n                type: \"critical_activity\",\n                userId: log.userId,\n                userName: log.userName,\n                severity: \"high\",\n                message: `نشاط حرج: ${log.action} في ${log.module}`,\n                details: log.details,\n                timestamp: new Date().toISOString(),\n                resolved: false,\n                relatedLogId: log.id\n            });\n        }\n        // تنبيه لحذف المستخدمين\n        if (log.action.includes(\"حذف مستخدم\")) {\n            alerts.push({\n                id: `user_deletion_${Date.now()}`,\n                type: \"user_deletion\",\n                userId: log.userId,\n                userName: log.userName,\n                severity: \"critical\",\n                message: `تم حذف مستخدم بواسطة ${log.userName}`,\n                details: log.details,\n                timestamp: new Date().toISOString(),\n                resolved: false,\n                relatedLogId: log.id\n            });\n        }\n        // تنبيه لتعديل الإعدادات\n        if (log.action.includes(\"تعديل الإعدادات\")) {\n            alerts.push({\n                id: `settings_change_${Date.now()}`,\n                type: \"settings_change\",\n                userId: log.userId,\n                userName: log.userName,\n                severity: \"medium\",\n                message: `تم تعديل إعدادات النظام بواسطة ${log.userName}`,\n                details: log.details,\n                timestamp: new Date().toISOString(),\n                resolved: false,\n                relatedLogId: log.id\n            });\n        }\n        // حفظ التنبيهات\n        if (alerts.length > 0) {\n            const transaction = this.db.transaction([\n                \"security_alerts\"\n            ], \"readwrite\");\n            const store = transaction.objectStore(\"security_alerts\");\n            alerts.forEach((alert)=>{\n                store.add(alert);\n            });\n        }\n    }\n    // فحص محاولات تسجيل الدخول المشبوهة\n    async checkSuspiciousLogins(log) {\n        if (!log.success) {\n            // فحص محاولات فاشلة متتالية\n            const recentFailures = await this.getRecentFailedLogins(log.userId, 5);\n            if (recentFailures.length >= 3) {\n                const alert = {\n                    id: `failed_logins_${Date.now()}`,\n                    type: \"multiple_failed_logins\",\n                    userId: log.userId,\n                    userName: log.userName,\n                    severity: \"high\",\n                    message: `محاولات تسجيل دخول فاشلة متعددة للمستخدم ${log.userName}`,\n                    details: `${recentFailures.length} محاولات فاشلة في آخر 5 دقائق`,\n                    timestamp: new Date().toISOString(),\n                    resolved: false,\n                    relatedLogId: log.id\n                };\n                const transaction = this.db.transaction([\n                    \"security_alerts\"\n                ], \"readwrite\");\n                const store = transaction.objectStore(\"security_alerts\");\n                store.add(alert);\n            }\n        }\n    }\n    // الحصول على محاولات تسجيل الدخول الفاشلة الأخيرة\n    async getRecentFailedLogins(userId, minutes) {\n        const cutoffTime = new Date(Date.now() - minutes * 60 * 1000).toISOString();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"login_logs\"\n            ], \"readonly\");\n            const store = transaction.objectStore(\"login_logs\");\n            const index = store.index(\"userId\");\n            const request = index.getAll(userId);\n            request.onsuccess = ()=>{\n                const failedLogins = request.result.filter((log)=>!log.success && log.timestamp >= cutoffTime).sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n                resolve(failedLogins);\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // الحصول على التنبيهات الأمنية\n    async getSecurityAlerts(filters) {\n        if (!this.db) await this.initDB();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"security_alerts\"\n            ], \"readonly\");\n            const store = transaction.objectStore(\"security_alerts\");\n            const request = store.getAll();\n            request.onsuccess = ()=>{\n                let alerts = request.result;\n                if (filters) {\n                    if (filters.resolved !== undefined) {\n                        alerts = alerts.filter((alert)=>alert.resolved === filters.resolved);\n                    }\n                    if (filters.severity) {\n                        alerts = alerts.filter((alert)=>alert.severity === filters.severity);\n                    }\n                }\n                alerts.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n                const limit = filters?.limit || 50;\n                resolve(alerts.slice(0, limit));\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // حل تنبيه أمني\n    async resolveSecurityAlert(alertId) {\n        if (!this.db) await this.initDB();\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"security_alerts\"\n            ], \"readwrite\");\n            const store = transaction.objectStore(\"security_alerts\");\n            const getRequest = store.get(alertId);\n            getRequest.onsuccess = ()=>{\n                const alert = getRequest.result;\n                if (alert) {\n                    alert.resolved = true;\n                    alert.resolvedAt = new Date().toISOString();\n                    const putRequest = store.put(alert);\n                    putRequest.onsuccess = ()=>resolve();\n                    putRequest.onerror = ()=>reject(putRequest.error);\n                } else {\n                    reject(new Error(\"Alert not found\"));\n                }\n            };\n            getRequest.onerror = ()=>reject(getRequest.error);\n        });\n    }\n    // أرشفة السجلات القديمة\n    async archiveOldLogs(daysToKeep = 90) {\n        if (!this.db) await this.initDB();\n        const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000).toISOString();\n        let archivedCount = 0;\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                \"activity_logs\"\n            ], \"readwrite\");\n            const store = transaction.objectStore(\"activity_logs\");\n            const index = store.index(\"timestamp\");\n            const range = IDBKeyRange.upperBound(cutoffDate);\n            const request = index.openCursor(range);\n            request.onsuccess = (event)=>{\n                const cursor = event.target.result;\n                if (cursor) {\n                    // يمكن هنا نقل السجل إلى أرشيف منفصل قبل الحذف\n                    cursor.delete();\n                    archivedCount++;\n                    cursor.continue();\n                } else {\n                    resolve(archivedCount);\n                }\n            };\n            request.onerror = ()=>reject(request.error);\n        });\n    }\n    // الحصول على إحصائيات متقدمة\n    async getAdvancedStats(days = 30) {\n        const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString();\n        const logs = await this.getAllLogs({\n            startDate,\n            limit: 10000\n        });\n        const stats = {\n            totalLogs: logs.length,\n            criticalLogs: logs.filter((log)=>log.severity === \"critical\").length,\n            highLogs: logs.filter((log)=>log.severity === \"high\").length,\n            mediumLogs: logs.filter((log)=>log.severity === \"medium\").length,\n            lowLogs: logs.filter((log)=>log.severity === \"low\").length,\n            moduleBreakdown: {},\n            actionBreakdown: {},\n            userBreakdown: {},\n            dailyBreakdown: {},\n            topUsers: [],\n            topActions: [],\n            topModules: []\n        };\n        // تجميع الإحصائيات\n        logs.forEach((log)=>{\n            // إحصائيات الموديولات\n            stats.moduleBreakdown[log.module] = (stats.moduleBreakdown[log.module] || 0) + 1;\n            // إحصائيات الأنشطة\n            stats.actionBreakdown[log.action] = (stats.actionBreakdown[log.action] || 0) + 1;\n            // إحصائيات المستخدمين\n            const userKey = `${log.userId}:${log.userName}`;\n            stats.userBreakdown[userKey] = (stats.userBreakdown[userKey] || 0) + 1;\n            // إحصائيات يومية\n            const day = log.timestamp.split(\"T\")[0];\n            stats.dailyBreakdown[day] = (stats.dailyBreakdown[day] || 0) + 1;\n        });\n        // ترتيب القوائم العلوية\n        stats.topUsers = Object.entries(stats.userBreakdown).map(([userKey, count])=>{\n            const [userId, userName] = userKey.split(\":\");\n            return {\n                userId,\n                userName,\n                count\n            };\n        }).sort((a, b)=>b.count - a.count).slice(0, 10);\n        stats.topActions = Object.entries(stats.actionBreakdown).map(([action, count])=>({\n                action,\n                count\n            })).sort((a, b)=>b.count - a.count).slice(0, 10);\n        stats.topModules = Object.entries(stats.moduleBreakdown).map(([module, count])=>({\n                module,\n                count\n            })).sort((a, b)=>b.count - a.count).slice(0, 10);\n        return stats;\n    }\n}\n// تصدير المثيل الوحيد\nconst activityLogsDB = ActivityLogsDatabase.getInstance();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/database/activity-logs-db.ts\n");

/***/ })

};
;