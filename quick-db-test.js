#!/usr/bin/env node

/**
 * اختبار سريع لقاعدة البيانات PostgreSQL
 * يختبر الاتصال الأساسي فقط
 */

const { Pool } = require('pg')

// إعدادات قاعدة البيانات
const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'V_Connect',
  user: 'openpg',
  password: 'V@admin010',
  connectionTimeoutMillis: 5000,
}

// ألوان للطباعة
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
}

// دالة للطباعة الملونة
const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

async function quickTest() {
  log('🔍 اختبار سريع لقاعدة البيانات PostgreSQL', 'blue')
  log('=' .repeat(50), 'blue')

  try {
    log('\n📡 محاولة الاتصال بقاعدة البيانات...', 'yellow')
    
    const pool = new Pool(dbConfig)
    const startTime = Date.now()
    
    // اختبار الاتصال
    const client = await pool.connect()
    const endTime = Date.now()
    
    log('✅ تم الاتصال بنجاح!', 'green')
    log(`⏱️ وقت الاتصال: ${endTime - startTime}ms`, 'blue')
    
    // الحصول على معلومات أساسية
    try {
      const timeResult = await client.query('SELECT NOW() as current_time')
      log(`🕐 الوقت الحالي: ${timeResult.rows[0].current_time}`, 'blue')
      
      const versionResult = await client.query('SELECT version()')
      const version = versionResult.rows[0].version.split(',')[0]
      log(`📊 إصدار PostgreSQL: ${version}`, 'blue')
      
      // التحقق من وجود قاعدة البيانات
      const dbResult = await client.query('SELECT current_database()')
      log(`🗄️ قاعدة البيانات الحالية: ${dbResult.rows[0].current_database}`, 'blue')
      
      // عدد الجداول
      const tablesResult = await client.query(`
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `)
      log(`📋 عدد الجداول: ${tablesResult.rows[0].count}`, 'blue')
      
    } catch (queryError) {
      log('⚠️ تم الاتصال ولكن فشل في تنفيذ بعض الاستعلامات:', 'yellow')
      log(queryError.message, 'yellow')
    }
    
    client.release()
    await pool.end()
    
    log('\n🎉 اختبار الاتصال نجح بالكامل!', 'green')
    log('✅ قاعدة البيانات جاهزة للاستخدام', 'green')
    
  } catch (error) {
    log('\n❌ فشل الاتصال بقاعدة البيانات:', 'red')
    log(`خطأ: ${error.message}`, 'red')
    
    // تشخيص المشكلة
    log('\n🔧 تشخيص المشكلة:', 'yellow')
    
    if (error.code === 'ECONNREFUSED') {
      log('- PostgreSQL غير مُشغل أو لا يستمع على المنفذ 5432', 'yellow')
      log('- تأكد من تشغيل خدمة PostgreSQL', 'yellow')
    } else if (error.code === 'ENOTFOUND') {
      log('- لا يمكن العثور على الخادم localhost', 'yellow')
      log('- تحقق من إعدادات الشبكة', 'yellow')
    } else if (error.message.includes('password authentication failed')) {
      log('- اسم المستخدم أو كلمة المرور غير صحيحة', 'yellow')
      log('- تحقق من بيانات الاعتماد', 'yellow')
    } else if (error.message.includes('database') && error.message.includes('does not exist')) {
      log('- قاعدة البيانات V_Connect غير موجودة', 'yellow')
      log('- قم بإنشاء قاعدة البيانات أولاً', 'yellow')
    } else if (error.code === 'ETIMEDOUT') {
      log('- انتهت مهلة الاتصال', 'yellow')
      log('- الخادم بطيء أو غير متاح', 'yellow')
    }
    
    log('\n💡 حلول مقترحة:', 'blue')
    log('1. تأكد من تشغيل PostgreSQL:', 'blue')
    log('   pg_isready -h localhost -p 5432', 'blue')
    log('2. تحقق من الخدمات:', 'blue')
    log('   Windows: services.msc', 'blue')
    log('   Linux: sudo systemctl status postgresql', 'blue')
    log('3. إنشاء قاعدة البيانات:', 'blue')
    log('   psql -U postgres -c "CREATE DATABASE \\"V_Connect\\""', 'blue')
    
    process.exit(1)
  }
  
  log('\n' + '=' .repeat(50), 'blue')
}

// تشغيل الاختبار
if (require.main === module) {
  quickTest().catch(error => {
    log('❌ خطأ غير متوقع:', 'red')
    log(error.message, 'red')
    process.exit(1)
  })
}

module.exports = { quickTest }
