#!/usr/bin/env node

/**
 * سكريبت اختبار شامل لقاعدة البيانات PostgreSQL
 * يختبر الاتصال، إنشاء الجداول، والعمليات الأساسية
 */

const { Pool } = require('pg')
const bcrypt = require('bcryptjs')

// إعدادات قاعدة البيانات
const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'V_Connect',
  user: 'openpg',
  password: 'V@admin010',
}

// ألوان للطباعة
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
}

// دالة للطباعة الملونة
const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// دالة اختبار الاتصال
async function testConnection() {
  log('\n🔍 اختبار الاتصال بقاعدة البيانات...', 'blue')
  
  try {
    const pool = new Pool(dbConfig)
    const client = await pool.connect()
    
    const result = await client.query('SELECT NOW() as current_time, version() as version')
    
    log('✅ تم الاتصال بقاعدة البيانات بنجاح!', 'green')
    log(`⏰ الوقت الحالي: ${result.rows[0].current_time}`, 'blue')
    log(`📊 إصدار PostgreSQL: ${result.rows[0].version.split(',')[0]}`, 'blue')
    
    client.release()
    await pool.end()
    return true
  } catch (error) {
    log('❌ فشل الاتصال بقاعدة البيانات:', 'red')
    log(error.message, 'red')
    return false
  }
}

// دالة إنشاء الجداول
async function createTables() {
  log('\n🏗️ إنشاء الجداول...', 'blue')
  
  const pool = new Pool(dbConfig)
  
  try {
    // جدول المستخدمين
    await pool.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(100) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(255) NOT NULL,
        role VARCHAR(50) NOT NULL DEFAULT 'employee',
        branch_id INTEGER,
        warehouse_id INTEGER,
        pos_id INTEGER,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    log('✅ تم إنشاء جدول المستخدمين', 'green')

    // جدول الفروع
    await pool.query(`
      CREATE TABLE IF NOT EXISTS branches (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        address TEXT,
        phone VARCHAR(50),
        email VARCHAR(255),
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    log('✅ تم إنشاء جدول الفروع', 'green')

    // جدول المخازن
    await pool.query(`
      CREATE TABLE IF NOT EXISTS warehouses (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        location TEXT,
        branch_id INTEGER REFERENCES branches(id),
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    log('✅ تم إنشاء جدول المخازن', 'green')

    // جدول المنتجات
    await pool.query(`
      CREATE TABLE IF NOT EXISTS products (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        sku VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        category VARCHAR(100),
        unit_price DECIMAL(15,2) NOT NULL,
        cost_price DECIMAL(15,2),
        stock_quantity INTEGER DEFAULT 0,
        min_stock_level INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    log('✅ تم إنشاء جدول المنتجات', 'green')

    // جدول العملاء
    await pool.query(`
      CREATE TABLE IF NOT EXISTS customers (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        phone VARCHAR(50),
        address TEXT,
        credit_limit DECIMAL(15,2) DEFAULT 0,
        current_balance DECIMAL(15,2) DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    log('✅ تم إنشاء جدول العملاء', 'green')

    await pool.end()
    return true
  } catch (error) {
    log('❌ خطأ في إنشاء الجداول:', 'red')
    log(error.message, 'red')
    await pool.end()
    return false
  }
}

// دالة اختبار العمليات الأساسية
async function testCRUDOperations() {
  log('\n🔧 اختبار العمليات الأساسية (CRUD)...', 'blue')
  
  const pool = new Pool(dbConfig)
  
  try {
    // اختبار الإدراج (Create)
    log('📝 اختبار إدراج البيانات...', 'yellow')
    
    // إدراج فرع تجريبي
    const branchResult = await pool.query(`
      INSERT INTO branches (name, address, phone, email) 
      VALUES ('فرع تجريبي', 'عنوان تجريبي', '+201234567890', '<EMAIL>')
      ON CONFLICT (name) DO UPDATE SET updated_at = CURRENT_TIMESTAMP
      RETURNING id
    `)
    const branchId = branchResult.rows[0]?.id || 1
    log('✅ تم إدراج الفرع التجريبي', 'green')

    // إدراج مخزن تجريبي
    await pool.query(`
      INSERT INTO warehouses (name, location, branch_id) 
      VALUES ('مخزن تجريبي', 'موقع تجريبي', $1)
      ON CONFLICT (name) DO UPDATE SET updated_at = CURRENT_TIMESTAMP
    `, [branchId])
    log('✅ تم إدراج المخزن التجريبي', 'green')

    // إدراج منتج تجريبي
    await pool.query(`
      INSERT INTO products (name, sku, description, category, unit_price, cost_price) 
      VALUES ('منتج تجريبي', 'TEST-001', 'وصف المنتج التجريبي', 'فئة تجريبية', 100.00, 80.00)
      ON CONFLICT (sku) DO UPDATE SET updated_at = CURRENT_TIMESTAMP
    `)
    log('✅ تم إدراج المنتج التجريبي', 'green')

    // إدراج عميل تجريبي
    await pool.query(`
      INSERT INTO customers (name, email, phone, address) 
      VALUES ('عميل تجريبي', '<EMAIL>', '+201234567891', 'عنوان العميل التجريبي')
      ON CONFLICT (email) DO UPDATE SET updated_at = CURRENT_TIMESTAMP
    `)
    log('✅ تم إدراج العميل التجريبي', 'green')

    // إدراج مستخدم تجريبي
    const hashedPassword = await bcrypt.hash('123456', 10)
    await pool.query(`
      INSERT INTO users (username, email, password_hash, full_name, role) 
      VALUES ('testuser', '<EMAIL>', $1, 'مستخدم تجريبي', 'employee')
      ON CONFLICT (username) DO UPDATE SET updated_at = CURRENT_TIMESTAMP
    `, [hashedPassword])
    log('✅ تم إدراج المستخدم التجريبي', 'green')

    // اختبار القراءة (Read)
    log('📖 اختبار قراءة البيانات...', 'yellow')
    
    const productsResult = await pool.query('SELECT COUNT(*) as count FROM products WHERE is_active = true')
    log(`✅ عدد المنتجات النشطة: ${productsResult.rows[0].count}`, 'green')

    const customersResult = await pool.query('SELECT COUNT(*) as count FROM customers WHERE is_active = true')
    log(`✅ عدد العملاء النشطين: ${customersResult.rows[0].count}`, 'green')

    const usersResult = await pool.query('SELECT COUNT(*) as count FROM users WHERE is_active = true')
    log(`✅ عدد المستخدمين النشطين: ${usersResult.rows[0].count}`, 'green')

    // اختبار التحديث (Update)
    log('✏️ اختبار تحديث البيانات...', 'yellow')
    
    await pool.query(`
      UPDATE products 
      SET unit_price = 120.00, updated_at = CURRENT_TIMESTAMP 
      WHERE sku = 'TEST-001'
    `)
    log('✅ تم تحديث سعر المنتج التجريبي', 'green')

    // اختبار البحث
    log('🔍 اختبار البحث في البيانات...', 'yellow')
    
    const searchResult = await pool.query(`
      SELECT name, sku, unit_price 
      FROM products 
      WHERE name ILIKE '%تجريبي%' AND is_active = true
    `)
    log(`✅ تم العثور على ${searchResult.rows.length} منتج في البحث`, 'green')

    await pool.end()
    return true
  } catch (error) {
    log('❌ خطأ في اختبار العمليات الأساسية:', 'red')
    log(error.message, 'red')
    await pool.end()
    return false
  }
}

// دالة اختبار الأداء
async function testPerformance() {
  log('\n⚡ اختبار الأداء...', 'blue')
  
  const pool = new Pool(dbConfig)
  
  try {
    // اختبار سرعة الاستعلامات
    const startTime = Date.now()
    
    await pool.query('SELECT * FROM products WHERE is_active = true LIMIT 100')
    
    const endTime = Date.now()
    const duration = endTime - startTime
    
    log(`✅ وقت تنفيذ استعلام المنتجات: ${duration}ms`, 'green')
    
    if (duration < 100) {
      log('🚀 الأداء ممتاز!', 'green')
    } else if (duration < 500) {
      log('👍 الأداء جيد', 'yellow')
    } else {
      log('⚠️ الأداء بطيء - قد تحتاج لتحسين', 'red')
    }

    await pool.end()
    return true
  } catch (error) {
    log('❌ خطأ في اختبار الأداء:', 'red')
    log(error.message, 'red')
    await pool.end()
    return false
  }
}

// دالة عرض إحصائيات قاعدة البيانات
async function showDatabaseStats() {
  log('\n📊 إحصائيات قاعدة البيانات...', 'blue')
  
  const pool = new Pool(dbConfig)
  
  try {
    // حجم قاعدة البيانات
    const sizeResult = await pool.query(`
      SELECT pg_size_pretty(pg_database_size('V_Connect')) as database_size
    `)
    log(`💾 حجم قاعدة البيانات: ${sizeResult.rows[0].database_size}`, 'blue')

    // عدد الجداول
    const tablesResult = await pool.query(`
      SELECT COUNT(*) as table_count 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `)
    log(`🗂️ عدد الجداول: ${tablesResult.rows[0].table_count}`, 'blue')

    // عدد الاتصالات النشطة
    const connectionsResult = await pool.query(`
      SELECT COUNT(*) as active_connections 
      FROM pg_stat_activity 
      WHERE datname = 'V_Connect'
    `)
    log(`🔗 عدد الاتصالات النشطة: ${connectionsResult.rows[0].active_connections}`, 'blue')

    await pool.end()
    return true
  } catch (error) {
    log('❌ خطأ في عرض الإحصائيات:', 'red')
    log(error.message, 'red')
    await pool.end()
    return false
  }
}

// الدالة الرئيسية
async function main() {
  log('🚀 بدء اختبار قاعدة البيانات PostgreSQL', 'blue')
  log('=' .repeat(50), 'blue')

  const tests = [
    { name: 'اختبار الاتصال', func: testConnection },
    { name: 'إنشاء الجداول', func: createTables },
    { name: 'اختبار العمليات الأساسية', func: testCRUDOperations },
    { name: 'اختبار الأداء', func: testPerformance },
    { name: 'عرض الإحصائيات', func: showDatabaseStats }
  ]

  let passedTests = 0
  let totalTests = tests.length

  for (const test of tests) {
    try {
      const result = await test.func()
      if (result) {
        passedTests++
      }
    } catch (error) {
      log(`❌ خطأ في ${test.name}: ${error.message}`, 'red')
    }
  }

  // النتائج النهائية
  log('\n' + '=' .repeat(50), 'blue')
  log('📋 ملخص نتائج الاختبار:', 'blue')
  log(`✅ نجح: ${passedTests}/${totalTests} اختبار`, passedTests === totalTests ? 'green' : 'yellow')
  
  if (passedTests === totalTests) {
    log('🎉 جميع الاختبارات نجحت! قاعدة البيانات تعمل بشكل مثالي.', 'green')
  } else {
    log('⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.', 'red')
  }
  
  log('=' .repeat(50), 'blue')
}

// تشغيل الاختبار
if (require.main === module) {
  main().catch(error => {
    log('❌ خطأ عام في تشغيل الاختبار:', 'red')
    log(error.message, 'red')
    process.exit(1)
  })
}

module.exports = {
  testConnection,
  createTables,
  testCRUDOperations,
  testPerformance,
  showDatabaseStats
}
