"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "./src/components/ui/NotificationCenter.tsx":
/*!**************************************************!*\
  !*** ./src/components/ui/NotificationCenter.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationToast: function() { return /* binding */ NotificationToast; },\n/* harmony export */   \"default\": function() { return /* binding */ NotificationCenter; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Check,CheckCircle,Info,X,XCircle!=!lucide-react */ \"__barrel_optimize__?names=AlertTriangle,Bell,Check,CheckCircle,Info,X,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"./src/components/ui/scroll-area.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\nfunction NotificationCenter() {\n    _s();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notificationManager, setNotificationManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // التأكد من أننا في المتصفح\n        setIsClient(true);\n        if (true) {\n            // استيراد نظام الإشعارات بشكل آمن\n            __webpack_require__.e(/*! import() */ \"src_lib_notifications_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/notifications */ \"./src/lib/notifications.ts\")).then((module)=>{\n                if (module.notificationManager) {\n                    setNotificationManager(module.notificationManager);\n                    // تحميل الإشعارات الأولية\n                    setNotifications(module.notificationManager.getNotifications());\n                    // الاستماع للتغييرات\n                    const unsubscribe = module.notificationManager.addListener((newNotifications)=>{\n                        setNotifications(newNotifications);\n                    });\n                    return unsubscribe;\n                }\n            }).catch(console.error);\n        }\n    }, []);\n    const unreadCount = notifications.filter((n)=>!n.read).length;\n    const handleMarkAsRead = (id)=>{\n        if (notificationManager) {\n            notificationManager.markAsRead(id);\n        }\n    };\n    const handleMarkAllAsRead = ()=>{\n        if (notificationManager) {\n            notificationManager.markAllAsRead();\n        }\n    };\n    const handleRemove = (id)=>{\n        if (notificationManager) {\n            notificationManager.removeNotification(id);\n        }\n    };\n    const handleClearAll = ()=>{\n        if (notificationManager) {\n            notificationManager.clearAll();\n        }\n        setIsOpen(false);\n    };\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__.CheckCircle, {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__.XCircle, {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 16\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__.AlertTriangle, {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Info, {\n                    className: \"h-4 w-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"urgent\":\n                return \"border-l-red-500 bg-red-50\";\n            case \"high\":\n                return \"border-l-orange-500 bg-orange-50\";\n            case \"medium\":\n                return \"border-l-blue-500 bg-blue-50\";\n            default:\n                return \"border-l-gray-500 bg-gray-50\";\n        }\n    };\n    const formatTime = (timestamp)=>{\n        const date = new Date(timestamp);\n        const now = new Date();\n        const diffMs = now.getTime() - date.getTime();\n        const diffMins = Math.floor(diffMs / 60000);\n        const diffHours = Math.floor(diffMins / 60);\n        const diffDays = Math.floor(diffHours / 24);\n        if (diffMins < 1) return \"الآن\";\n        if (diffMins < 60) return \"منذ \".concat(diffMins, \" دقيقة\");\n        if (diffHours < 24) return \"منذ \".concat(diffHours, \" ساعة\");\n        if (diffDays < 7) return \"منذ \".concat(diffDays, \" يوم\");\n        return date.toLocaleDateString(\"ar-EG\");\n    };\n    // عدم عرض المكون في الخادم\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n            variant: \"ghost\",\n            size: \"sm\",\n            className: \"relative\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Bell, {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n        open: isOpen,\n        onOpenChange: setIsOpen,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"ghost\",\n                    size: \"sm\",\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Bell, {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                            variant: \"destructive\",\n                            className: \"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs\",\n                            children: unreadCount > 99 ? \"99+\" : unreadCount\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                align: \"end\",\n                className: \"w-80 p-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"border-0 shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            className: \"pb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"الإشعارات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 space-x-reverse\",\n                                            children: [\n                                                unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: handleMarkAllAsRead,\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Check, {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"قراءة الكل\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 19\n                                                }, this),\n                                                notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: handleClearAll,\n                                                    className: \"text-xs text-red-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__.X, {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"مسح الكل\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: [\n                                        \"لديك \",\n                                        unreadCount,\n                                        \" إشعار غير مقروء\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-0\",\n                            children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 text-center text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Bell, {\n                                        className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"لا توجد إشعارات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                                className: \"h-96\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1 p-2\",\n                                    children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\\n                        p-3 rounded-lg border-l-4 cursor-pointer transition-colors\\n                        \".concat(getPriorityColor(notification.priority), \"\\n                        \").concat(notification.read ? \"opacity-60\" : \"\", \"\\n                        hover:bg-opacity-80\\n                      \"),\n                                            onClick: ()=>!notification.read && handleMarkAsRead(notification.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-3 space-x-reverse flex-1\",\n                                                            children: [\n                                                                getNotificationIcon(notification.type),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"text-sm font-medium text-gray-900 truncate\",\n                                                                                    children: notification.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                                                    lineNumber: 216,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 bg-blue-500 rounded-full ml-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                                                    lineNumber: 220,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                                            lineNumber: 215,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-600 mt-1 line-clamp-2\",\n                                                                            children: notification.message\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between mt-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-gray-500\",\n                                                                                    children: formatTime(notification.timestamp)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                                                    lineNumber: 227,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: notification.module\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                                                    lineNumber: 230,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                                            lineNumber: 226,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                handleRemove(notification.id);\n                                                            },\n                                                            className: \"h-6 w-6 p-0 ml-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__.X, {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 23\n                                                }, this),\n                                                notification.actions && notification.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2 space-x-reverse mt-3\",\n                                                    children: notification.actions.map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: action.style === \"primary\" ? \"default\" : \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"text-xs\",\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                // تنفيذ الإجراء\n                                                                console.log(\"تنفيذ الإجراء:\", action.action);\n                                                            },\n                                                            children: action.label\n                                                        }, action.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 29\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, notification.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationCenter, \"RkRK852tEbV38r499/RxLlUFqCY=\");\n_c = NotificationCenter;\n// مكون إشعار منبثق للإشعارات العاجلة\nfunction NotificationToast(param) {\n    let { notification, onClose } = param;\n    _s1();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // إغلاق تلقائي بعد 5 ثوان للإشعارات غير العاجلة\n        if (notification.priority !== \"urgent\") {\n            const timer = setTimeout(onClose, 5000);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        notification.priority,\n        onClose\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n      fixed top-4 right-4 z-50 w-80 p-4 rounded-lg shadow-lg border-l-4\\n      \".concat(getPriorityColor(notification.priority), \"\\n      animate-in slide-in-from-right duration-300\\n    \"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-3 space-x-reverse flex-1\",\n                    children: [\n                        getNotificationIcon(notification.type),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-900\",\n                                    children: notification.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600 mt-1\",\n                                    children: notification.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"ghost\",\n                    size: \"sm\",\n                    onClick: onClose,\n                    className: \"h-6 w-6 p-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__.X, {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n            lineNumber: 299,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n        lineNumber: 294,\n        columnNumber: 5\n    }, this);\n}\n_s1(NotificationToast, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c1 = NotificationToast;\n// دالة مساعدة للألوان (نفس الدالة من الأعلى)\nfunction getPriorityColor(priority) {\n    switch(priority){\n        case \"urgent\":\n            return \"border-l-red-500 bg-red-50\";\n        case \"high\":\n            return \"border-l-orange-500 bg-orange-50\";\n        case \"medium\":\n            return \"border-l-blue-500 bg-blue-50\";\n        default:\n            return \"border-l-gray-500 bg-gray-50\";\n    }\n}\n// دالة مساعدة للأيقونات (نفس الدالة من الأعلى)\nfunction getNotificationIcon(type) {\n    switch(type){\n        case \"success\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__.CheckCircle, {\n                className: \"h-4 w-4 text-green-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                lineNumber: 342,\n                columnNumber: 14\n            }, this);\n        case \"error\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__.XCircle, {\n                className: \"h-4 w-4 text-red-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                lineNumber: 344,\n                columnNumber: 14\n            }, this);\n        case \"warning\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__.AlertTriangle, {\n                className: \"h-4 w-4 text-yellow-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                lineNumber: 346,\n                columnNumber: 14\n            }, this);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Info, {\n                className: \"h-4 w-4 text-blue-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\NotificationCenter.tsx\",\n                lineNumber: 348,\n                columnNumber: 14\n            }, this);\n    }\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"NotificationCenter\");\n$RefreshReg$(_c1, \"NotificationToast\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy91aS9Ob3RpZmljYXRpb25DZW50ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTJDO0FBRTZDO0FBQ3pDO0FBQ0Y7QUFDa0M7QUFLekM7QUFDa0I7QUFvQnpDLFNBQVNtQjs7SUFDdEIsTUFBTSxDQUFDQyxlQUFlQyxpQkFBaUIsR0FBR3JCLCtDQUFRQSxDQUFpQixFQUFFO0lBQ3JFLE1BQU0sQ0FBQ3NCLFFBQVFDLFVBQVUsR0FBR3ZCLCtDQUFRQSxDQUFDO0lBQ3JDLE1BQU0sQ0FBQ3dCLFVBQVVDLFlBQVksR0FBR3pCLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQzBCLHFCQUFxQkMsdUJBQXVCLEdBQUczQiwrQ0FBUUEsQ0FBTTtJQUVwRUMsZ0RBQVNBLENBQUM7UUFDUiw0QkFBNEI7UUFDNUJ3QixZQUFZO1FBRVosSUFBSSxJQUFrQixFQUFhO1lBQ2pDLGtDQUFrQztZQUNsQyw4S0FBTyxDQUF1QkcsSUFBSSxDQUFDLENBQUNDO2dCQUNsQyxJQUFJQSxPQUFPSCxtQkFBbUIsRUFBRTtvQkFDOUJDLHVCQUF1QkUsT0FBT0gsbUJBQW1CO29CQUNqRCwwQkFBMEI7b0JBQzFCTCxpQkFBaUJRLE9BQU9ILG1CQUFtQixDQUFDSSxnQkFBZ0I7b0JBRTVELHFCQUFxQjtvQkFDckIsTUFBTUMsY0FBY0YsT0FBT0gsbUJBQW1CLENBQUNNLFdBQVcsQ0FBQyxDQUFDQzt3QkFDMURaLGlCQUFpQlk7b0JBQ25CO29CQUVBLE9BQU9GO2dCQUNUO1lBQ0YsR0FBR0csS0FBSyxDQUFDQyxRQUFRQyxLQUFLO1FBQ3hCO0lBQ0YsR0FBRyxFQUFFO0lBRUwsTUFBTUMsY0FBY2pCLGNBQWNrQixNQUFNLENBQUNDLENBQUFBLElBQUssQ0FBQ0EsRUFBRUMsSUFBSSxFQUFFQyxNQUFNO0lBRTdELE1BQU1DLG1CQUFtQixDQUFDQztRQUN4QixJQUFJakIscUJBQXFCO1lBQ3ZCQSxvQkFBb0JrQixVQUFVLENBQUNEO1FBQ2pDO0lBQ0Y7SUFFQSxNQUFNRSxzQkFBc0I7UUFDMUIsSUFBSW5CLHFCQUFxQjtZQUN2QkEsb0JBQW9Cb0IsYUFBYTtRQUNuQztJQUNGO0lBRUEsTUFBTUMsZUFBZSxDQUFDSjtRQUNwQixJQUFJakIscUJBQXFCO1lBQ3ZCQSxvQkFBb0JzQixrQkFBa0IsQ0FBQ0w7UUFDekM7SUFDRjtJQUVBLE1BQU1NLGlCQUFpQjtRQUNyQixJQUFJdkIscUJBQXFCO1lBQ3ZCQSxvQkFBb0J3QixRQUFRO1FBQzlCO1FBQ0EzQixVQUFVO0lBQ1o7SUFFQSxNQUFNNEIsc0JBQXNCLENBQUNDO1FBQzNCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxxQkFBTyw4REFBQzdDLGdJQUFXQTtvQkFBQzhDLFdBQVU7Ozs7OztZQUNoQyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDN0MsNEhBQU9BO29CQUFDNkMsV0FBVTs7Ozs7O1lBQzVCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNoRCxrSUFBYUE7b0JBQUNnRCxXQUFVOzs7Ozs7WUFDbEM7Z0JBQ0UscUJBQU8sOERBQUMvQyx5SEFBSUE7b0JBQUMrQyxXQUFVOzs7Ozs7UUFDM0I7SUFDRjtJQUVBLE1BQU1DLG1CQUFtQixDQUFDQztRQUN4QixPQUFRQTtZQUNOLEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNUO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsTUFBTUMsYUFBYSxDQUFDQztRQUNsQixNQUFNQyxPQUFPLElBQUlDLEtBQUtGO1FBQ3RCLE1BQU1HLE1BQU0sSUFBSUQ7UUFDaEIsTUFBTUUsU0FBU0QsSUFBSUUsT0FBTyxLQUFLSixLQUFLSSxPQUFPO1FBQzNDLE1BQU1DLFdBQVdDLEtBQUtDLEtBQUssQ0FBQ0osU0FBUztRQUNyQyxNQUFNSyxZQUFZRixLQUFLQyxLQUFLLENBQUNGLFdBQVc7UUFDeEMsTUFBTUksV0FBV0gsS0FBS0MsS0FBSyxDQUFDQyxZQUFZO1FBRXhDLElBQUlILFdBQVcsR0FBRyxPQUFPO1FBQ3pCLElBQUlBLFdBQVcsSUFBSSxPQUFPLE9BQWdCLE9BQVRBLFVBQVM7UUFDMUMsSUFBSUcsWUFBWSxJQUFJLE9BQU8sT0FBaUIsT0FBVkEsV0FBVTtRQUM1QyxJQUFJQyxXQUFXLEdBQUcsT0FBTyxPQUFnQixPQUFUQSxVQUFTO1FBQ3pDLE9BQU9ULEtBQUtVLGtCQUFrQixDQUFDO0lBQ2pDO0lBRUEsMkJBQTJCO0lBQzNCLElBQUksQ0FBQzVDLFVBQVU7UUFDYixxQkFDRSw4REFBQ2YseURBQU1BO1lBQUM0RCxTQUFRO1lBQVFDLE1BQUs7WUFBS2pCLFdBQVU7c0JBQzFDLDRFQUFDbkQseUhBQUlBO2dCQUFDbUQsV0FBVTs7Ozs7Ozs7Ozs7SUFHdEI7SUFFQSxxQkFDRSw4REFBQ3RDLHNFQUFZQTtRQUFDd0QsTUFBTWpEO1FBQVFrRCxjQUFjakQ7OzBCQUN4Qyw4REFBQ04sNkVBQW1CQTtnQkFBQ3dELE9BQU87MEJBQzFCLDRFQUFDaEUseURBQU1BO29CQUFDNEQsU0FBUTtvQkFBUUMsTUFBSztvQkFBS2pCLFdBQVU7O3NDQUMxQyw4REFBQ25ELHlIQUFJQTs0QkFBQ21ELFdBQVU7Ozs7Ozt3QkFDZmhCLGNBQWMsbUJBQ2IsOERBQUMzQix1REFBS0E7NEJBQ0oyRCxTQUFROzRCQUNSaEIsV0FBVTtzQ0FFVGhCLGNBQWMsS0FBSyxRQUFRQTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTXBDLDhEQUFDckIsNkVBQW1CQTtnQkFBQzBELE9BQU07Z0JBQU1yQixXQUFVOzBCQUN6Qyw0RUFBQzFDLHFEQUFJQTtvQkFBQzBDLFdBQVU7O3NDQUNkLDhEQUFDeEMsMkRBQVVBOzRCQUFDd0MsV0FBVTs7OENBQ3BCLDhEQUFDc0I7b0NBQUl0QixXQUFVOztzREFDYiw4REFBQ3ZDLDBEQUFTQTs0Q0FBQ3VDLFdBQVU7c0RBQVU7Ozs7OztzREFDL0IsOERBQUNzQjs0Q0FBSXRCLFdBQVU7O2dEQUNaaEIsY0FBYyxtQkFDYiw4REFBQzVCLHlEQUFNQTtvREFDTDRELFNBQVE7b0RBQ1JDLE1BQUs7b0RBQ0xNLFNBQVMvQjtvREFDVFEsV0FBVTs7c0VBRVYsOERBQUNqRCwwSEFBS0E7NERBQUNpRCxXQUFVOzs7Ozs7d0RBQWlCOzs7Ozs7O2dEQUlyQ2pDLGNBQWNxQixNQUFNLEdBQUcsbUJBQ3RCLDhEQUFDaEMseURBQU1BO29EQUNMNEQsU0FBUTtvREFDUkMsTUFBSztvREFDTE0sU0FBUzNCO29EQUNUSSxXQUFVOztzRUFFViw4REFBQ2xELHNIQUFDQTs0REFBQ2tELFdBQVU7Ozs7Ozt3REFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBTXJDaEIsY0FBYyxtQkFDYiw4REFBQ3dDO29DQUFFeEIsV0FBVTs7d0NBQWdDO3dDQUNyQ2hCO3dDQUFZOzs7Ozs7Ozs7Ozs7O3NDQUt4Qiw4REFBQ3pCLDREQUFXQTs0QkFBQ3lDLFdBQVU7c0NBQ3BCakMsY0FBY3FCLE1BQU0sS0FBSyxrQkFDeEIsOERBQUNrQztnQ0FBSXRCLFdBQVU7O2tEQUNiLDhEQUFDbkQseUhBQUlBO3dDQUFDbUQsV0FBVTs7Ozs7O2tEQUNoQiw4REFBQ3dCO2tEQUFFOzs7Ozs7Ozs7OztxREFHTCw4REFBQzNELGtFQUFVQTtnQ0FBQ21DLFdBQVU7MENBQ3BCLDRFQUFDc0I7b0NBQUl0QixXQUFVOzhDQUNaakMsY0FBYzBELEdBQUcsQ0FBQyxDQUFDQyw2QkFDbEIsOERBQUNKOzRDQUVDdEIsV0FBVyxpSEFHUDBCLE9BREF6QixpQkFBaUJ5QixhQUFheEIsUUFBUSxHQUFFLDhCQUNGLE9BQXRDd0IsYUFBYXZDLElBQUksR0FBRyxlQUFlLElBQUc7NENBRzFDb0MsU0FBUyxJQUFNLENBQUNHLGFBQWF2QyxJQUFJLElBQUlFLGlCQUFpQnFDLGFBQWFwQyxFQUFFOzs4REFFckUsOERBQUNnQztvREFBSXRCLFdBQVU7O3NFQUNiLDhEQUFDc0I7NERBQUl0QixXQUFVOztnRUFDWkYsb0JBQW9CNEIsYUFBYTNCLElBQUk7OEVBQ3RDLDhEQUFDdUI7b0VBQUl0QixXQUFVOztzRkFDYiw4REFBQ3NCOzRFQUFJdEIsV0FBVTs7OEZBQ2IsOERBQUMyQjtvRkFBRzNCLFdBQVU7OEZBQ1gwQixhQUFhRSxLQUFLOzs7Ozs7Z0ZBRXBCLENBQUNGLGFBQWF2QyxJQUFJLGtCQUNqQiw4REFBQ21DO29GQUFJdEIsV0FBVTs7Ozs7Ozs7Ozs7O3NGQUduQiw4REFBQ3dCOzRFQUFFeEIsV0FBVTtzRkFDVjBCLGFBQWFHLE9BQU87Ozs7OztzRkFFdkIsOERBQUNQOzRFQUFJdEIsV0FBVTs7OEZBQ2IsOERBQUM4QjtvRkFBSzlCLFdBQVU7OEZBQ2JHLFdBQVd1QixhQUFhdEIsU0FBUzs7Ozs7OzhGQUVwQyw4REFBQy9DLHVEQUFLQTtvRkFBQzJELFNBQVE7b0ZBQVVoQixXQUFVOzhGQUNoQzBCLGFBQWFsRCxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBSzVCLDhEQUFDcEIseURBQU1BOzREQUNMNEQsU0FBUTs0REFDUkMsTUFBSzs0REFDTE0sU0FBUyxDQUFDUTtnRUFDUkEsRUFBRUMsZUFBZTtnRUFDakJ0QyxhQUFhZ0MsYUFBYXBDLEVBQUU7NERBQzlCOzREQUNBVSxXQUFVO3NFQUVWLDRFQUFDbEQsc0hBQUNBO2dFQUFDa0QsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0RBSWhCMEIsYUFBYU8sT0FBTyxJQUFJUCxhQUFhTyxPQUFPLENBQUM3QyxNQUFNLEdBQUcsbUJBQ3JELDhEQUFDa0M7b0RBQUl0QixXQUFVOzhEQUNaMEIsYUFBYU8sT0FBTyxDQUFDUixHQUFHLENBQUMsQ0FBQ1MsdUJBQ3pCLDhEQUFDOUUseURBQU1BOzREQUVMNEQsU0FBU2tCLE9BQU9DLEtBQUssS0FBSyxZQUFZLFlBQVk7NERBQ2xEbEIsTUFBSzs0REFDTGpCLFdBQVU7NERBQ1Z1QixTQUFTLENBQUNRO2dFQUNSQSxFQUFFQyxlQUFlO2dFQUNqQixnQkFBZ0I7Z0VBQ2hCbEQsUUFBUXNELEdBQUcsQ0FBQyxrQkFBa0JGLE9BQU9BLE1BQU07NERBQzdDO3NFQUVDQSxPQUFPRyxLQUFLOzJEQVZSSCxPQUFPNUMsRUFBRTs7Ozs7Ozs7Ozs7MkNBbkRqQm9DLGFBQWFwQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBNEUxQztHQXRQd0J4QjtLQUFBQTtBQXdQeEIscUNBQXFDO0FBQzlCLFNBQVN3RSxrQkFBa0IsS0FHakM7UUFIaUMsRUFBRVosWUFBWSxFQUFFYSxPQUFPLEVBR3hELEdBSGlDOztJQUloQzNGLGdEQUFTQSxDQUFDO1FBQ1IsZ0RBQWdEO1FBQ2hELElBQUk4RSxhQUFheEIsUUFBUSxLQUFLLFVBQVU7WUFDdEMsTUFBTXNDLFFBQVFDLFdBQVdGLFNBQVM7WUFDbEMsT0FBTyxJQUFNRyxhQUFhRjtRQUM1QjtJQUNGLEdBQUc7UUFBQ2QsYUFBYXhCLFFBQVE7UUFBRXFDO0tBQVE7SUFFbkMscUJBQ0UsOERBQUNqQjtRQUFJdEIsV0FBVyxvRkFFNEIsT0FBeENDLGlCQUFpQnlCLGFBQWF4QixRQUFRLEdBQUU7a0JBRzFDLDRFQUFDb0I7WUFBSXRCLFdBQVU7OzhCQUNiLDhEQUFDc0I7b0JBQUl0QixXQUFVOzt3QkFDWkYsb0JBQW9CNEIsYUFBYTNCLElBQUk7c0NBQ3RDLDhEQUFDdUI7NEJBQUl0QixXQUFVOzs4Q0FDYiw4REFBQzJCO29DQUFHM0IsV0FBVTs4Q0FDWDBCLGFBQWFFLEtBQUs7Ozs7Ozs4Q0FFckIsOERBQUNKO29DQUFFeEIsV0FBVTs4Q0FDVjBCLGFBQWFHLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFJM0IsOERBQUN6RSx5REFBTUE7b0JBQ0w0RCxTQUFRO29CQUNSQyxNQUFLO29CQUNMTSxTQUFTZ0I7b0JBQ1R2QyxXQUFVOzhCQUVWLDRFQUFDbEQsc0hBQUNBO3dCQUFDa0QsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUt2QjtJQXpDZ0JzQztNQUFBQTtBQTJDaEIsNkNBQTZDO0FBQzdDLFNBQVNyQyxpQkFBaUJDLFFBQWtDO0lBQzFELE9BQVFBO1FBQ04sS0FBSztZQUNILE9BQU87UUFDVCxLQUFLO1lBQ0gsT0FBTztRQUNULEtBQUs7WUFDSCxPQUFPO1FBQ1Q7WUFDRSxPQUFPO0lBQ1g7QUFDRjtBQUVBLCtDQUErQztBQUMvQyxTQUFTSixvQkFBb0JDLElBQTBCO0lBQ3JELE9BQVFBO1FBQ04sS0FBSztZQUNILHFCQUFPLDhEQUFDN0MsZ0lBQVdBO2dCQUFDOEMsV0FBVTs7Ozs7O1FBQ2hDLEtBQUs7WUFDSCxxQkFBTyw4REFBQzdDLDRIQUFPQTtnQkFBQzZDLFdBQVU7Ozs7OztRQUM1QixLQUFLO1lBQ0gscUJBQU8sOERBQUNoRCxrSUFBYUE7Z0JBQUNnRCxXQUFVOzs7Ozs7UUFDbEM7WUFDRSxxQkFBTyw4REFBQy9DLHlIQUFJQTtnQkFBQytDLFdBQVU7Ozs7OztJQUMzQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL3VpL05vdGlmaWNhdGlvbkNlbnRlci50c3g/MzI4NiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgZHluYW1pYyBmcm9tICduZXh0L2R5bmFtaWMnXG5pbXBvcnQgeyBCZWxsLCBYLCBDaGVjaywgQWxlcnRUcmlhbmdsZSwgSW5mbywgQ2hlY2tDaXJjbGUsIFhDaXJjbGUgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnXG5pbXBvcnQge1xuICBEcm9wZG93bk1lbnUsXG4gIERyb3Bkb3duTWVudUNvbnRlbnQsXG4gIERyb3Bkb3duTWVudVRyaWdnZXIsXG59IGZyb20gJ0AvY29tcG9uZW50cy91aS9kcm9wZG93bi1tZW51J1xuaW1wb3J0IHsgU2Nyb2xsQXJlYSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9zY3JvbGwtYXJlYSdcblxuLy8g2KrYudix2YrZgSDZhtmI2Lkg2KfZhNil2LTYudin2LEg2YXYrdmE2YrYp9mLXG5pbnRlcmZhY2UgTm90aWZpY2F0aW9uIHtcbiAgaWQ6IHN0cmluZ1xuICB0aXRsZTogc3RyaW5nXG4gIG1lc3NhZ2U6IHN0cmluZ1xuICB0eXBlOiAnc3VjY2VzcycgfCAnZXJyb3InIHwgJ3dhcm5pbmcnIHwgJ2luZm8nXG4gIHByaW9yaXR5OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnIHwgJ3VyZ2VudCdcbiAgbW9kdWxlOiBzdHJpbmdcbiAgdGltZXN0YW1wOiBzdHJpbmdcbiAgcmVhZDogYm9vbGVhblxuICBhY3Rpb25zPzogQXJyYXk8e1xuICAgIGlkOiBzdHJpbmdcbiAgICBsYWJlbDogc3RyaW5nXG4gICAgYWN0aW9uOiBzdHJpbmdcbiAgICBzdHlsZT86ICdwcmltYXJ5JyB8ICdzZWNvbmRhcnknIHwgJ2RhbmdlcidcbiAgfT5cbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTm90aWZpY2F0aW9uQ2VudGVyKCkge1xuICBjb25zdCBbbm90aWZpY2F0aW9ucywgc2V0Tm90aWZpY2F0aW9uc10gPSB1c2VTdGF0ZTxOb3RpZmljYXRpb25bXT4oW10pXG4gIGNvbnN0IFtpc09wZW4sIHNldElzT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2lzQ2xpZW50LCBzZXRJc0NsaWVudF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW25vdGlmaWNhdGlvbk1hbmFnZXIsIHNldE5vdGlmaWNhdGlvbk1hbmFnZXJdID0gdXNlU3RhdGU8YW55PihudWxsKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8g2KfZhNiq2KPZg9ivINmF2YYg2KPZhtmG2Kcg2YHZiiDYp9mE2YXYqti12YHYrVxuICAgIHNldElzQ2xpZW50KHRydWUpXG5cbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIC8vINin2LPYqtmK2LHYp9ivINmG2LjYp9mFINin2YTYpdi02LnYp9ix2KfYqiDYqNi02YPZhCDYotmF2YZcbiAgICAgIGltcG9ydCgnQC9saWIvbm90aWZpY2F0aW9ucycpLnRoZW4oKG1vZHVsZSkgPT4ge1xuICAgICAgICBpZiAobW9kdWxlLm5vdGlmaWNhdGlvbk1hbmFnZXIpIHtcbiAgICAgICAgICBzZXROb3RpZmljYXRpb25NYW5hZ2VyKG1vZHVsZS5ub3RpZmljYXRpb25NYW5hZ2VyKVxuICAgICAgICAgIC8vINiq2K3ZhdmK2YQg2KfZhNil2LTYudin2LHYp9iqINin2YTYo9mI2YTZitipXG4gICAgICAgICAgc2V0Tm90aWZpY2F0aW9ucyhtb2R1bGUubm90aWZpY2F0aW9uTWFuYWdlci5nZXROb3RpZmljYXRpb25zKCkpXG5cbiAgICAgICAgICAvLyDYp9mE2KfYs9iq2YXYp9i5INmE2YTYqti62YrZitix2KfYqlxuICAgICAgICAgIGNvbnN0IHVuc3Vic2NyaWJlID0gbW9kdWxlLm5vdGlmaWNhdGlvbk1hbmFnZXIuYWRkTGlzdGVuZXIoKG5ld05vdGlmaWNhdGlvbnM6IE5vdGlmaWNhdGlvbltdKSA9PiB7XG4gICAgICAgICAgICBzZXROb3RpZmljYXRpb25zKG5ld05vdGlmaWNhdGlvbnMpXG4gICAgICAgICAgfSlcblxuICAgICAgICAgIHJldHVybiB1bnN1YnNjcmliZVxuICAgICAgICB9XG4gICAgICB9KS5jYXRjaChjb25zb2xlLmVycm9yKVxuICAgIH1cbiAgfSwgW10pXG5cbiAgY29uc3QgdW5yZWFkQ291bnQgPSBub3RpZmljYXRpb25zLmZpbHRlcihuID0+ICFuLnJlYWQpLmxlbmd0aFxuXG4gIGNvbnN0IGhhbmRsZU1hcmtBc1JlYWQgPSAoaWQ6IHN0cmluZykgPT4ge1xuICAgIGlmIChub3RpZmljYXRpb25NYW5hZ2VyKSB7XG4gICAgICBub3RpZmljYXRpb25NYW5hZ2VyLm1hcmtBc1JlYWQoaWQpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlTWFya0FsbEFzUmVhZCA9ICgpID0+IHtcbiAgICBpZiAobm90aWZpY2F0aW9uTWFuYWdlcikge1xuICAgICAgbm90aWZpY2F0aW9uTWFuYWdlci5tYXJrQWxsQXNSZWFkKClcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVSZW1vdmUgPSAoaWQ6IHN0cmluZykgPT4ge1xuICAgIGlmIChub3RpZmljYXRpb25NYW5hZ2VyKSB7XG4gICAgICBub3RpZmljYXRpb25NYW5hZ2VyLnJlbW92ZU5vdGlmaWNhdGlvbihpZClcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVDbGVhckFsbCA9ICgpID0+IHtcbiAgICBpZiAobm90aWZpY2F0aW9uTWFuYWdlcikge1xuICAgICAgbm90aWZpY2F0aW9uTWFuYWdlci5jbGVhckFsbCgpXG4gICAgfVxuICAgIHNldElzT3BlbihmYWxzZSlcbiAgfVxuXG4gIGNvbnN0IGdldE5vdGlmaWNhdGlvbkljb24gPSAodHlwZTogTm90aWZpY2F0aW9uWyd0eXBlJ10pID0+IHtcbiAgICBzd2l0Y2ggKHR5cGUpIHtcbiAgICAgIGNhc2UgJ3N1Y2Nlc3MnOlxuICAgICAgICByZXR1cm4gPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICByZXR1cm4gPFhDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXJlZC01MDBcIiAvPlxuICAgICAgY2FzZSAnd2FybmluZyc6XG4gICAgICAgIHJldHVybiA8QWxlcnRUcmlhbmdsZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQteWVsbG93LTUwMFwiIC8+XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gPEluZm8gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWJsdWUtNTAwXCIgLz5cbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRQcmlvcml0eUNvbG9yID0gKHByaW9yaXR5OiBOb3RpZmljYXRpb25bJ3ByaW9yaXR5J10pID0+IHtcbiAgICBzd2l0Y2ggKHByaW9yaXR5KSB7XG4gICAgICBjYXNlICd1cmdlbnQnOlxuICAgICAgICByZXR1cm4gJ2JvcmRlci1sLXJlZC01MDAgYmctcmVkLTUwJ1xuICAgICAgY2FzZSAnaGlnaCc6XG4gICAgICAgIHJldHVybiAnYm9yZGVyLWwtb3JhbmdlLTUwMCBiZy1vcmFuZ2UtNTAnXG4gICAgICBjYXNlICdtZWRpdW0nOlxuICAgICAgICByZXR1cm4gJ2JvcmRlci1sLWJsdWUtNTAwIGJnLWJsdWUtNTAnXG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gJ2JvcmRlci1sLWdyYXktNTAwIGJnLWdyYXktNTAnXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZm9ybWF0VGltZSA9ICh0aW1lc3RhbXA6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSh0aW1lc3RhbXApXG4gICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKVxuICAgIGNvbnN0IGRpZmZNcyA9IG5vdy5nZXRUaW1lKCkgLSBkYXRlLmdldFRpbWUoKVxuICAgIGNvbnN0IGRpZmZNaW5zID0gTWF0aC5mbG9vcihkaWZmTXMgLyA2MDAwMClcbiAgICBjb25zdCBkaWZmSG91cnMgPSBNYXRoLmZsb29yKGRpZmZNaW5zIC8gNjApXG4gICAgY29uc3QgZGlmZkRheXMgPSBNYXRoLmZsb29yKGRpZmZIb3VycyAvIDI0KVxuXG4gICAgaWYgKGRpZmZNaW5zIDwgMSkgcmV0dXJuICfYp9mE2KLZhidcbiAgICBpZiAoZGlmZk1pbnMgPCA2MCkgcmV0dXJuIGDZhdmG2LAgJHtkaWZmTWluc30g2K/ZgtmK2YLYqWBcbiAgICBpZiAoZGlmZkhvdXJzIDwgMjQpIHJldHVybiBg2YXZhtiwICR7ZGlmZkhvdXJzfSDYs9in2LnYqWBcbiAgICBpZiAoZGlmZkRheXMgPCA3KSByZXR1cm4gYNmF2YbYsCAke2RpZmZEYXlzfSDZitmI2YVgXG4gICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCdhci1FRycpXG4gIH1cblxuICAvLyDYudiv2YUg2LnYsdi2INin2YTZhdmD2YjZhiDZgdmKINin2YTYrtin2K/ZhVxuICBpZiAoIWlzQ2xpZW50KSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgPEJlbGwgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICA8L0J1dHRvbj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxEcm9wZG93bk1lbnUgb3Blbj17aXNPcGVufSBvbk9wZW5DaGFuZ2U9e3NldElzT3Blbn0+XG4gICAgICA8RHJvcGRvd25NZW51VHJpZ2dlciBhc0NoaWxkPlxuICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgPEJlbGwgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAge3VucmVhZENvdW50ID4gMCAmJiAoXG4gICAgICAgICAgICA8QmFkZ2VcbiAgICAgICAgICAgICAgdmFyaWFudD1cImRlc3RydWN0aXZlXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0xIC1yaWdodC0xIGgtNSB3LTUgcm91bmRlZC1mdWxsIHAtMCB0ZXh0LXhzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge3VucmVhZENvdW50ID4gOTkgPyAnOTkrJyA6IHVucmVhZENvdW50fVxuICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICApfVxuICAgICAgICA8L0J1dHRvbj5cbiAgICAgIDwvRHJvcGRvd25NZW51VHJpZ2dlcj5cblxuICAgICAgPERyb3Bkb3duTWVudUNvbnRlbnQgYWxpZ249XCJlbmRcIiBjbGFzc05hbWU9XCJ3LTgwIHAtMFwiPlxuICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJib3JkZXItMCBzaGFkb3ctbGdcIj5cbiAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0zXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGdcIj7Yp9mE2KXYtNi52KfYsdin2Ko8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgc3BhY2UteC1yZXZlcnNlXCI+XG4gICAgICAgICAgICAgICAge3VucmVhZENvdW50ID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZU1hcmtBbGxBc1JlYWR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHNcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8Q2hlY2sgY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAg2YLYsdin2KHYqSDYp9mE2YPZhFxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9ucy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ2xlYXJBbGx9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1yZWQtNjAwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAg2YXYs9itINin2YTZg9mEXG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAge3VucmVhZENvdW50ID4gMCAmJiAoXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAg2YTYr9mK2YMge3VucmVhZENvdW50fSDYpdi02LnYp9ixINi62YrYsSDZhdmC2LHZiNihXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9DYXJkSGVhZGVyPlxuXG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtMFwiPlxuICAgICAgICAgICAge25vdGlmaWNhdGlvbnMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiB0ZXh0LWNlbnRlciB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICA8QmVsbCBjbGFzc05hbWU9XCJoLTggdy04IG14LWF1dG8gbWItMiBvcGFjaXR5LTUwXCIgLz5cbiAgICAgICAgICAgICAgICA8cD7ZhNinINiq2YjYrNivINil2LTYudin2LHYp9iqPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxTY3JvbGxBcmVhIGNsYXNzTmFtZT1cImgtOTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMSBwLTJcIj5cbiAgICAgICAgICAgICAgICAgIHtub3RpZmljYXRpb25zLm1hcCgobm90aWZpY2F0aW9uKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e25vdGlmaWNhdGlvbi5pZH1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgICAgICAgICAgIHAtMyByb3VuZGVkLWxnIGJvcmRlci1sLTQgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1jb2xvcnNcbiAgICAgICAgICAgICAgICAgICAgICAgICR7Z2V0UHJpb3JpdHlDb2xvcihub3RpZmljYXRpb24ucHJpb3JpdHkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgJHtub3RpZmljYXRpb24ucmVhZCA/ICdvcGFjaXR5LTYwJyA6ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgaG92ZXI6Ymctb3BhY2l0eS04MFxuICAgICAgICAgICAgICAgICAgICAgIGB9XG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gIW5vdGlmaWNhdGlvbi5yZWFkICYmIGhhbmRsZU1hcmtBc1JlYWQobm90aWZpY2F0aW9uLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTMgc3BhY2UteC1yZXZlcnNlIGZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Tm90aWZpY2F0aW9uSWNvbihub3RpZmljYXRpb24udHlwZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9uLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHshbm90aWZpY2F0aW9uLnJlYWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmx1ZS01MDAgcm91bmRlZC1mdWxsIG1sLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS02MDAgbXQtMSBsaW5lLWNsYW1wLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtub3RpZmljYXRpb24ubWVzc2FnZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbXQtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRUaW1lKG5vdGlmaWNhdGlvbi50aW1lc3RhbXApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9uLm1vZHVsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVJlbW92ZShub3RpZmljYXRpb24uaWQpXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNiB3LTYgcC0wIG1sLTJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAge25vdGlmaWNhdGlvbi5hY3Rpb25zICYmIG5vdGlmaWNhdGlvbi5hY3Rpb25zLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMiBzcGFjZS14LXJldmVyc2UgbXQtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9uLmFjdGlvbnMubWFwKChhY3Rpb24pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2FjdGlvbi5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9e2FjdGlvbi5zdHlsZSA9PT0gJ3ByaW1hcnknID8gJ2RlZmF1bHQnIDogJ291dGxpbmUnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDYqtmG2YHZitiwINin2YTYpdis2LHYp9ihXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfYqtmG2YHZitiwINin2YTYpdis2LHYp9ihOicsIGFjdGlvbi5hY3Rpb24pXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHthY3Rpb24ubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9TY3JvbGxBcmVhPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICA8L0Ryb3Bkb3duTWVudUNvbnRlbnQ+XG4gICAgPC9Ecm9wZG93bk1lbnU+XG4gIClcbn1cblxuLy8g2YXZg9mI2YYg2KXYtNi52KfYsSDZhdmG2KjYq9mCINmE2YTYpdi02LnYp9ix2KfYqiDYp9mE2LnYp9is2YTYqVxuZXhwb3J0IGZ1bmN0aW9uIE5vdGlmaWNhdGlvblRvYXN0KHsgbm90aWZpY2F0aW9uLCBvbkNsb3NlIH06IHtcbiAgbm90aWZpY2F0aW9uOiBOb3RpZmljYXRpb25cbiAgb25DbG9zZTogKCkgPT4gdm9pZFxufSkge1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vINil2LrZhNin2YIg2KrZhNmC2KfYptmKINio2LnYryA1INir2YjYp9mGINmE2YTYpdi02LnYp9ix2KfYqiDYutmK2LEg2KfZhNi52KfYrNmE2KlcbiAgICBpZiAobm90aWZpY2F0aW9uLnByaW9yaXR5ICE9PSAndXJnZW50Jykge1xuICAgICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KG9uQ2xvc2UsIDUwMDApXG4gICAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKVxuICAgIH1cbiAgfSwgW25vdGlmaWNhdGlvbi5wcmlvcml0eSwgb25DbG9zZV0pXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YFxuICAgICAgZml4ZWQgdG9wLTQgcmlnaHQtNCB6LTUwIHctODAgcC00IHJvdW5kZWQtbGcgc2hhZG93LWxnIGJvcmRlci1sLTRcbiAgICAgICR7Z2V0UHJpb3JpdHlDb2xvcihub3RpZmljYXRpb24ucHJpb3JpdHkpfVxuICAgICAgYW5pbWF0ZS1pbiBzbGlkZS1pbi1mcm9tLXJpZ2h0IGR1cmF0aW9uLTMwMFxuICAgIGB9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zIHNwYWNlLXgtcmV2ZXJzZSBmbGV4LTFcIj5cbiAgICAgICAgICB7Z2V0Tm90aWZpY2F0aW9uSWNvbihub3RpZmljYXRpb24udHlwZSl9XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAge25vdGlmaWNhdGlvbi50aXRsZX1cbiAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS02MDAgbXQtMVwiPlxuICAgICAgICAgICAgICB7bm90aWZpY2F0aW9uLm1lc3NhZ2V9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgIG9uQ2xpY2s9e29uQ2xvc2V9XG4gICAgICAgICAgY2xhc3NOYW1lPVwiaC02IHctNiBwLTBcIlxuICAgICAgICA+XG4gICAgICAgICAgPFggY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgIDwvQnV0dG9uPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cblxuLy8g2K/Yp9mE2Kkg2YXYs9in2LnYr9ipINmE2YTYo9mE2YjYp9mGICjZhtmB2LMg2KfZhNiv2KfZhNipINmF2YYg2KfZhNij2LnZhNmJKVxuZnVuY3Rpb24gZ2V0UHJpb3JpdHlDb2xvcihwcmlvcml0eTogTm90aWZpY2F0aW9uWydwcmlvcml0eSddKSB7XG4gIHN3aXRjaCAocHJpb3JpdHkpIHtcbiAgICBjYXNlICd1cmdlbnQnOlxuICAgICAgcmV0dXJuICdib3JkZXItbC1yZWQtNTAwIGJnLXJlZC01MCdcbiAgICBjYXNlICdoaWdoJzpcbiAgICAgIHJldHVybiAnYm9yZGVyLWwtb3JhbmdlLTUwMCBiZy1vcmFuZ2UtNTAnXG4gICAgY2FzZSAnbWVkaXVtJzpcbiAgICAgIHJldHVybiAnYm9yZGVyLWwtYmx1ZS01MDAgYmctYmx1ZS01MCdcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuICdib3JkZXItbC1ncmF5LTUwMCBiZy1ncmF5LTUwJ1xuICB9XG59XG5cbi8vINiv2KfZhNipINmF2LPYp9i52K/YqSDZhNmE2KPZitmC2YjZhtin2KogKNmG2YHYsyDYp9mE2K/Yp9mE2Kkg2YXZhiDYp9mE2KPYudmE2YkpXG5mdW5jdGlvbiBnZXROb3RpZmljYXRpb25JY29uKHR5cGU6IE5vdGlmaWNhdGlvblsndHlwZSddKSB7XG4gIHN3aXRjaCAodHlwZSkge1xuICAgIGNhc2UgJ3N1Y2Nlc3MnOlxuICAgICAgcmV0dXJuIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JlZW4tNTAwXCIgLz5cbiAgICBjYXNlICdlcnJvcic6XG4gICAgICByZXR1cm4gPFhDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXJlZC01MDBcIiAvPlxuICAgIGNhc2UgJ3dhcm5pbmcnOlxuICAgICAgcmV0dXJuIDxBbGVydFRyaWFuZ2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC15ZWxsb3ctNTAwXCIgLz5cbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuIDxJbmZvIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ibHVlLTUwMFwiIC8+XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkJlbGwiLCJYIiwiQ2hlY2siLCJBbGVydFRyaWFuZ2xlIiwiSW5mbyIsIkNoZWNrQ2lyY2xlIiwiWENpcmNsZSIsIkJ1dHRvbiIsIkJhZGdlIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkRyb3Bkb3duTWVudSIsIkRyb3Bkb3duTWVudUNvbnRlbnQiLCJEcm9wZG93bk1lbnVUcmlnZ2VyIiwiU2Nyb2xsQXJlYSIsIk5vdGlmaWNhdGlvbkNlbnRlciIsIm5vdGlmaWNhdGlvbnMiLCJzZXROb3RpZmljYXRpb25zIiwiaXNPcGVuIiwic2V0SXNPcGVuIiwiaXNDbGllbnQiLCJzZXRJc0NsaWVudCIsIm5vdGlmaWNhdGlvbk1hbmFnZXIiLCJzZXROb3RpZmljYXRpb25NYW5hZ2VyIiwidGhlbiIsIm1vZHVsZSIsImdldE5vdGlmaWNhdGlvbnMiLCJ1bnN1YnNjcmliZSIsImFkZExpc3RlbmVyIiwibmV3Tm90aWZpY2F0aW9ucyIsImNhdGNoIiwiY29uc29sZSIsImVycm9yIiwidW5yZWFkQ291bnQiLCJmaWx0ZXIiLCJuIiwicmVhZCIsImxlbmd0aCIsImhhbmRsZU1hcmtBc1JlYWQiLCJpZCIsIm1hcmtBc1JlYWQiLCJoYW5kbGVNYXJrQWxsQXNSZWFkIiwibWFya0FsbEFzUmVhZCIsImhhbmRsZVJlbW92ZSIsInJlbW92ZU5vdGlmaWNhdGlvbiIsImhhbmRsZUNsZWFyQWxsIiwiY2xlYXJBbGwiLCJnZXROb3RpZmljYXRpb25JY29uIiwidHlwZSIsImNsYXNzTmFtZSIsImdldFByaW9yaXR5Q29sb3IiLCJwcmlvcml0eSIsImZvcm1hdFRpbWUiLCJ0aW1lc3RhbXAiLCJkYXRlIiwiRGF0ZSIsIm5vdyIsImRpZmZNcyIsImdldFRpbWUiLCJkaWZmTWlucyIsIk1hdGgiLCJmbG9vciIsImRpZmZIb3VycyIsImRpZmZEYXlzIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwidmFyaWFudCIsInNpemUiLCJvcGVuIiwib25PcGVuQ2hhbmdlIiwiYXNDaGlsZCIsImFsaWduIiwiZGl2Iiwib25DbGljayIsInAiLCJtYXAiLCJub3RpZmljYXRpb24iLCJoNCIsInRpdGxlIiwibWVzc2FnZSIsInNwYW4iLCJlIiwic3RvcFByb3BhZ2F0aW9uIiwiYWN0aW9ucyIsImFjdGlvbiIsInN0eWxlIiwibG9nIiwibGFiZWwiLCJOb3RpZmljYXRpb25Ub2FzdCIsIm9uQ2xvc2UiLCJ0aW1lciIsInNldFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/ui/NotificationCenter.tsx\n"));

/***/ })

});