{"name": "business-management-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test-db": "node test-database.js", "test-db-quick": "node quick-db-test.js", "setup": "npm install && node test-database.js && npm run build"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/auth-helpers-react": "^0.4.2", "@supabase/supabase-js": "^2.38.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/pg": "^8.15.2", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "file-saver": "^2.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "next": "14.0.4", "next-i18next": "^15.2.0", "pg": "^8.16.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-i18next": "^13.5.0", "recharts": "^2.8.0", "tailwind-merge": "^2.1.0", "xlsx": "^0.18.5", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/file-saver": "^2.0.7", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3"}}