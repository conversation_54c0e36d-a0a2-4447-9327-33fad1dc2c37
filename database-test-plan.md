# خطة اختبار قاعدة البيانات PostgreSQL

## 1. التحقق من إعدادات قاعدة البيانات

### إعدادات قاعدة البيانات الحالية:
- **المضيف**: localhost
- **المنفذ**: 5432
- **قاعدة البيانات**: V_Connect
- **المستخدم**: openpg
- **كلمة المرور**: V@admin010

### الخطوات المطلوبة:

#### أ) التحقق من تشغيل PostgreSQL
```bash
# التحقق من حالة الخدمة
pg_isready -h localhost -p 5432

# أو التحقق من العمليات
tasklist | findstr postgres
```

#### ب) التحقق من وجود قاعدة البيانات
```sql
-- الاتصال بـ PostgreSQL
psql -h localhost -p 5432 -U openpg -d postgres

-- التحقق من قواعد البيانات الموجودة
\l

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
CREATE DATABASE "V_Connect";
```

#### ج) التحقق من صلاحيات المستخدم
```sql
-- التحقق من صلاحيات المستخدم openpg
\du openpg

-- منح الصلاحيات المطلوبة
GRANT ALL PRIVILEGES ON DATABASE "V_Connect" TO openpg;
```

## 2. اختبار الاتصال بقاعدة البيانات

### أ) اختبار API للاتصال
```bash
# تشغيل الخادم
npm run dev

# اختبار الاتصال
curl http://localhost:3000/api/test-db
```

### ب) اختبار من خلال واجهة المستخدم
1. افتح المتصفح على `http://localhost:3000`
2. اذهب إلى صفحة إعداد النظام
3. اختبر الاتصال بقاعدة البيانات

## 3. إنشاء وتهيئة الجداول

### أ) تشغيل سكريبت إنشاء الجداول
```bash
# تنفيذ سكريبت SQL
psql -h localhost -p 5432 -U openpg -d V_Connect -f database/01_create_tables.sql
```

### ب) التحقق من إنشاء الجداول
```sql
-- الاتصال بقاعدة البيانات
psql -h localhost -p 5432 -U openpg -d V_Connect

-- عرض الجداول
\dt

-- التحقق من بنية الجداول
\d users
\d products
\d customers
\d branches
\d warehouses
```

## 4. اختبار العمليات الأساسية (CRUD)

### أ) اختبار إدراج البيانات
```sql
-- إدراج مستخدم تجريبي
INSERT INTO users (username, email, password_hash, full_name, role) 
VALUES ('admin', '<EMAIL>', '$2a$10$hash', 'المدير العام', 'admin');

-- إدراج منتج تجريبي
INSERT INTO products (name, sku, description, category, unit_price, cost_price) 
VALUES ('منتج تجريبي', 'TEST-001', 'وصف المنتج', 'فئة تجريبية', 100.00, 80.00);
```

### ب) اختبار قراءة البيانات
```sql
-- قراءة المستخدمين
SELECT * FROM users;

-- قراءة المنتجات
SELECT * FROM products;

-- قراءة العملاء
SELECT * FROM customers;
```

### ج) اختبار تحديث البيانات
```sql
-- تحديث منتج
UPDATE products SET unit_price = 120.00 WHERE sku = 'TEST-001';

-- تحديث مستخدم
UPDATE users SET full_name = 'المدير المحدث' WHERE username = 'admin';
```

### د) اختبار حذف البيانات
```sql
-- حذف ناعم (تعطيل)
UPDATE products SET is_active = false WHERE sku = 'TEST-001';

-- حذف فعلي (للاختبار فقط)
DELETE FROM products WHERE sku = 'TEST-001';
```

## 5. اختبار وظائف التطبيق

### أ) اختبار نظام المصادقة
1. إنشاء حساب مدير جديد
2. تسجيل الدخول
3. تسجيل الخروج
4. اختبار صلاحيات المستخدمين

### ب) اختبار إدارة المنتجات
1. إضافة منتج جديد
2. تعديل منتج موجود
3. البحث عن منتجات
4. حذف منتج

### ج) اختبار إدارة العملاء
1. إضافة عميل جديد
2. تعديل بيانات عميل
3. البحث عن عملاء
4. عرض تفاصيل عميل

### د) اختبار المبيعات
1. إنشاء عرض أسعار
2. تحويل عرض الأسعار إلى طلب
3. تحويل الطلب إلى فاتورة
4. معالجة المرتجعات

## 6. اختبار الأداء

### أ) اختبار سرعة الاستعلامات
```sql
-- قياس وقت تنفيذ الاستعلامات
EXPLAIN ANALYZE SELECT * FROM products WHERE is_active = true;
EXPLAIN ANALYZE SELECT * FROM customers WHERE name ILIKE '%أحمد%';
```

### ب) اختبار الحمولة
1. إدراج كمية كبيرة من البيانات
2. تنفيذ استعلامات متعددة في نفس الوقت
3. مراقبة استخدام الذاكرة والمعالج

## 7. اختبار النسخ الاحتياطي والاستعادة

### أ) إنشاء نسخة احتياطية
```bash
pg_dump -h localhost -p 5432 -U openpg -d V_Connect > backup.sql
```

### ب) استعادة النسخة الاحتياطية
```bash
psql -h localhost -p 5432 -U openpg -d V_Connect_Test < backup.sql
```

## 8. اختبار الأمان

### أ) اختبار حقن SQL
1. محاولة إدخال أكواد SQL ضارة في النماذج
2. التحقق من تنظيف المدخلات
3. اختبار الاستعلامات المحضرة

### ب) اختبار الصلاحيات
1. اختبار وصول المستخدمين للبيانات
2. التحقق من تشفير كلمات المرور
3. اختبار انتهاء صلاحية الجلسات

## 9. مراقبة الأخطاء والسجلات

### أ) مراقبة سجلات PostgreSQL
```bash
# عرض سجلات PostgreSQL
tail -f /var/log/postgresql/postgresql-*.log
```

### ب) مراقبة سجلات التطبيق
1. فحص console.log في المتصفح
2. مراقبة سجلات الخادم
3. تتبع الأخطاء في قاعدة البيانات

## 10. اختبار التكامل مع الوحدات

### أ) اختبار تكامل المخزون
1. إضافة منتج وتحديث المخزون
2. إنشاء فاتورة مبيعات وخصم المخزون
3. معالجة المرتجعات وإضافة للمخزون

### ب) اختبار تكامل المحاسبة
1. إنشاء فاتورة وتسجيل القيد المحاسبي
2. تسجيل دفعة وتحديث الرصيد
3. إنشاء التقارير المالية

## نتائج الاختبار المتوقعة

✅ **نجح الاختبار إذا:**
- تم الاتصال بقاعدة البيانات بنجاح
- تم إنشاء جميع الجداول المطلوبة
- تعمل جميع العمليات الأساسية (CRUD)
- لا توجد أخطاء في السجلات
- الأداء مقبول للاستعلامات
- البيانات محفوظة بشكل صحيح

❌ **فشل الاختبار إذا:**
- فشل الاتصال بقاعدة البيانات
- أخطاء في إنشاء الجداول
- فشل في العمليات الأساسية
- وجود أخطاء متكررة في السجلات
- بطء شديد في الاستعلامات
- فقدان أو تلف في البيانات
