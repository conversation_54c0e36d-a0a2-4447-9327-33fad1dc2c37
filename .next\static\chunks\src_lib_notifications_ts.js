"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_lib_notifications_ts"],{

/***/ "./src/lib/notifications.ts":
/*!**********************************!*\
  !*** ./src/lib/notifications.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   notificationManager: function() { return /* binding */ notificationManager; },\n/* harmony export */   showErrorNotification: function() { return /* binding */ showErrorNotification; },\n/* harmony export */   showInfoNotification: function() { return /* binding */ showInfoNotification; },\n/* harmony export */   showInventoryAlert: function() { return /* binding */ showInventoryAlert; },\n/* harmony export */   showMaintenanceAlert: function() { return /* binding */ showMaintenanceAlert; },\n/* harmony export */   showSalesNotification: function() { return /* binding */ showSalesNotification; },\n/* harmony export */   showSuccessNotification: function() { return /* binding */ showSuccessNotification; },\n/* harmony export */   showWarningNotification: function() { return /* binding */ showWarningNotification; }\n/* harmony export */ });\n// نظام الإشعارات المحسن\nclass NotificationManager {\n    // طلب إذن المتصفح للإشعارات\n    async requestBrowserPermission() {\n        if ( true && \"Notification\" in window && Notification.permission === \"default\") {\n            await Notification.requestPermission();\n        }\n    }\n    // تحميل الإعدادات من التخزين المحلي\n    loadSettings() {\n        if (false) {}\n        try {\n            const saved = localStorage.getItem(\"notification_settings\");\n            if (saved) {\n                this.settings = {\n                    ...this.settings,\n                    ...JSON.parse(saved)\n                };\n            }\n        } catch (error) {\n            console.warn(\"فشل في تحميل إعدادات الإشعارات\");\n        }\n    }\n    // حفظ الإعدادات\n    saveSettings(settings) {\n        this.settings = {\n            ...this.settings,\n            ...settings\n        };\n        if (false) {}\n        try {\n            localStorage.setItem(\"notification_settings\", JSON.stringify(this.settings));\n        } catch (error) {\n            console.warn(\"فشل في حفظ إعدادات الإشعارات\");\n        }\n    }\n    // إضافة إشعار جديد\n    addNotification(notification) {\n        // التحقق من إعدادات النوع\n        if (!this.settings.notificationTypes[notification.module]) {\n            return;\n        }\n        // التحقق من الساعات الهادئة\n        if (this.isQuietTime()) {\n            return;\n        }\n        const newNotification = {\n            ...notification,\n            id: Date.now().toString(),\n            timestamp: new Date().toISOString(),\n            read: false\n        };\n        this.notifications.unshift(newNotification);\n        // إرسال إشعار المتصفح\n        if (this.settings.enableBrowserNotifications) {\n            this.sendBrowserNotification(newNotification);\n        }\n        // تشغيل الصوت\n        if (this.settings.enableSoundNotifications) {\n            this.playNotificationSound(newNotification.type);\n        }\n        // إشعار المستمعين\n        this.notifyListeners();\n        return newNotification.id;\n    }\n    // إرسال إشعار المتصفح\n    sendBrowserNotification(notification) {\n        if ( true && \"Notification\" in window && Notification.permission === \"granted\") {\n            const browserNotification = new Notification(notification.title, {\n                body: notification.message,\n                icon: this.getNotificationIcon(notification.type),\n                tag: notification.id,\n                requireInteraction: notification.priority === \"urgent\"\n            });\n            browserNotification.onclick = ()=>{\n                window.focus();\n                this.markAsRead(notification.id);\n                browserNotification.close();\n            };\n            // إغلاق تلقائي بعد 5 ثوان (إلا إذا كان عاجل)\n            if (notification.priority !== \"urgent\") {\n                setTimeout(()=>{\n                    browserNotification.close();\n                }, 5000);\n            }\n        }\n    }\n    // تشغيل صوت الإشعار\n    playNotificationSound(type) {\n        try {\n            const audio = new Audio();\n            switch(type){\n                case \"success\":\n                    audio.src = \"/sounds/success.mp3\";\n                    break;\n                case \"error\":\n                    audio.src = \"/sounds/error.mp3\";\n                    break;\n                case \"warning\":\n                    audio.src = \"/sounds/warning.mp3\";\n                    break;\n                default:\n                    audio.src = \"/sounds/notification.mp3\";\n            }\n            audio.volume = 0.3;\n            audio.play().catch(()=>{\n            // تجاهل أخطاء تشغيل الصوت\n            });\n        } catch (error) {\n        // تجاهل أخطاء الصوت\n        }\n    }\n    // الحصول على أيقونة الإشعار\n    getNotificationIcon(type) {\n        switch(type){\n            case \"success\":\n                return \"/icons/success.png\";\n            case \"error\":\n                return \"/icons/error.png\";\n            case \"warning\":\n                return \"/icons/warning.png\";\n            default:\n                return \"/icons/info.png\";\n        }\n    }\n    // التحقق من الساعات الهادئة\n    isQuietTime() {\n        if (!this.settings.quietHours.enabled) {\n            return false;\n        }\n        const now = new Date();\n        const currentTime = now.getHours() * 60 + now.getMinutes();\n        const [startHour, startMin] = this.settings.quietHours.start.split(\":\").map(Number);\n        const [endHour, endMin] = this.settings.quietHours.end.split(\":\").map(Number);\n        const startTime = startHour * 60 + startMin;\n        const endTime = endHour * 60 + endMin;\n        if (startTime <= endTime) {\n            return currentTime >= startTime && currentTime <= endTime;\n        } else {\n            // عبر منتصف الليل\n            return currentTime >= startTime || currentTime <= endTime;\n        }\n    }\n    // وضع علامة مقروء\n    markAsRead(id) {\n        const notification = this.notifications.find((n)=>n.id === id);\n        if (notification) {\n            notification.read = true;\n            this.notifyListeners();\n        }\n    }\n    // وضع علامة مقروء على الكل\n    markAllAsRead() {\n        this.notifications.forEach((n)=>n.read = true);\n        this.notifyListeners();\n    }\n    // حذف إشعار\n    removeNotification(id) {\n        this.notifications = this.notifications.filter((n)=>n.id !== id);\n        this.notifyListeners();\n    }\n    // مسح جميع الإشعارات\n    clearAll() {\n        this.notifications = [];\n        this.notifyListeners();\n    }\n    // الحصول على جميع الإشعارات\n    getNotifications() {\n        return [\n            ...this.notifications\n        ];\n    }\n    // الحصول على الإشعارات غير المقروءة\n    getUnreadNotifications() {\n        return this.notifications.filter((n)=>!n.read);\n    }\n    // الحصول على عدد الإشعارات غير المقروءة\n    getUnreadCount() {\n        return this.notifications.filter((n)=>!n.read).length;\n    }\n    // إضافة مستمع للتغييرات\n    addListener(callback) {\n        this.listeners.push(callback);\n        return ()=>{\n            this.listeners = this.listeners.filter((l)=>l !== callback);\n        };\n    }\n    // إشعار المستمعين\n    notifyListeners() {\n        this.listeners.forEach((callback)=>{\n            try {\n                callback([\n                    ...this.notifications\n                ]);\n            } catch (error) {\n                console.error(\"خطأ في مستمع الإشعارات:\", error);\n            }\n        });\n    }\n    // الحصول على الإعدادات\n    getSettings() {\n        return {\n            ...this.settings\n        };\n    }\n    constructor(){\n        this.notifications = [];\n        this.settings = {\n            enableBrowserNotifications: true,\n            enableSoundNotifications: true,\n            enableEmailNotifications: false,\n            enableWhatsAppNotifications: false,\n            notificationTypes: {\n                sales: true,\n                purchases: true,\n                inventory: true,\n                maintenance: true,\n                accounting: true,\n                system: true\n            },\n            quietHours: {\n                enabled: false,\n                start: \"22:00\",\n                end: \"08:00\"\n            }\n        };\n        this.listeners = [];\n        // تأخير التهيئة حتى يتم تحميل المتصفح\n        if (true) {\n            this.loadSettings();\n            this.requestBrowserPermission();\n        }\n    }\n}\n// إنشاء مثيل واحد - فقط في المتصفح\nconst notificationManager =  true ? new NotificationManager() : 0;\n// دوال مساعدة للاستخدام السريع\nconst showSuccessNotification = function(title, message) {\n    let module = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"system\";\n    if (!notificationManager) return null;\n    return notificationManager.addNotification({\n        title,\n        message,\n        type: \"success\",\n        priority: \"medium\",\n        module\n    });\n};\nconst showErrorNotification = function(title, message) {\n    let module = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"system\";\n    if (!notificationManager) return null;\n    return notificationManager.addNotification({\n        title,\n        message,\n        type: \"error\",\n        priority: \"high\",\n        module\n    });\n};\nconst showWarningNotification = function(title, message) {\n    let module = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"system\";\n    if (!notificationManager) return null;\n    return notificationManager.addNotification({\n        title,\n        message,\n        type: \"warning\",\n        priority: \"medium\",\n        module\n    });\n};\nconst showInfoNotification = function(title, message) {\n    let module = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"system\";\n    if (!notificationManager) return null;\n    return notificationManager.addNotification({\n        title,\n        message,\n        type: \"info\",\n        priority: \"low\",\n        module\n    });\n};\n// إشعارات خاصة بالوحدات\nconst showSalesNotification = function(title, message) {\n    let priority = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"medium\";\n    if (!notificationManager) return null;\n    return notificationManager.addNotification({\n        title,\n        message,\n        type: \"info\",\n        priority,\n        module: \"sales\"\n    });\n};\nconst showInventoryAlert = (title, message)=>{\n    if (!notificationManager) return null;\n    return notificationManager.addNotification({\n        title,\n        message,\n        type: \"warning\",\n        priority: \"high\",\n        module: \"inventory\"\n    });\n};\nconst showMaintenanceAlert = function(title, message) {\n    let priority = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"high\";\n    if (!notificationManager) return null;\n    return notificationManager.addNotification({\n        title,\n        message,\n        type: \"warning\",\n        priority,\n        module: \"maintenance\"\n    });\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/notifications.ts\n"));

/***/ })

}]);