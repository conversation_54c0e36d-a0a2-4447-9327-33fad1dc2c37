@echo off
chcp 65001 >nul
echo ========================================
echo    اختبار شامل لنظام إدارة الأعمال
echo ========================================
echo.

echo 🔍 التحقق من متطلبات النظام...
echo.

REM التحقق من Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً
    pause
    exit /b 1
)
echo ✅ Node.js مثبت: 
node --version

REM التحقق من npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح
    pause
    exit /b 1
)
echo ✅ npm مثبت: 
npm --version

REM التحقق من PostgreSQL
pg_isready -h localhost -p 5432 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PostgreSQL غير متاح أو لا يعمل على localhost:5432
    echo    يرجى التأكد من تشغيل PostgreSQL
    pause
    exit /b 1
)
echo ✅ PostgreSQL يعمل على localhost:5432

echo.
echo ========================================
echo    تثبيت التبعيات
echo ========================================
echo.

echo 📦 تثبيت تبعيات Node.js...
npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت التبعيات
    pause
    exit /b 1
)
echo ✅ تم تثبيت التبعيات بنجاح

echo.
echo ========================================
echo    اختبار قاعدة البيانات
echo ========================================
echo.

echo 🗄️ تشغيل اختبار قاعدة البيانات...
node test-database.js
if %errorlevel% neq 0 (
    echo ❌ فشل اختبار قاعدة البيانات
    echo    يرجى مراجعة الأخطاء أعلاه
    pause
    exit /b 1
)

echo.
echo ========================================
echo    بناء التطبيق
echo ========================================
echo.

echo 🔨 بناء التطبيق...
npm run build
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء التطبيق
    echo    يرجى مراجعة الأخطاء أعلاه
    pause
    exit /b 1
)
echo ✅ تم بناء التطبيق بنجاح

echo.
echo ========================================
echo    تشغيل التطبيق
echo ========================================
echo.

echo 🚀 تشغيل التطبيق...
echo    سيتم فتح التطبيق على http://localhost:3000
echo    اضغط Ctrl+C لإيقاف التطبيق
echo.

start http://localhost:3000
npm start

echo.
echo ========================================
echo    انتهى الاختبار
echo ========================================
pause
